{"data_mtime": 1750533056, "dep_lines": [4, 1, 2, 3, 76, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["email.mime.text", "logging", "smtplib", "ssl", "traceback", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "email", "email._policybase", "email.message", "email.mime", "email.mime.base", "email.mime.nonmultipart", "enum", "os", "typing"], "hash": "d59143404903662150dcb690c6110af337b8256f", "id": "SMTP同步独立测试", "ignore_all": false, "interface_hash": "749e31904e17c5d216ad98bbde915585765f1ccf", "mtime": 1748281748, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\base\\CMS\\payload_cms\\scripts\\SMTP同步独立测试.py", "plugin_data": null, "size": 2349, "suppressed": [], "version_id": "1.15.0"}