{".class": "MypyFile", "_fullname": "asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ALL_COMPLETED": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.ALL_COMPLETED", "kind": "Gdef"}, "AbstractEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.AbstractEventLoop", "kind": "Gdef"}, "AbstractEventLoopPolicy": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.AbstractEventLoopPolicy", "kind": "Gdef"}, "AbstractServer": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.AbstractServer", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Barrier": {".class": "SymbolTableNode", "cross_ref": "asyncio.locks.Barrier", "kind": "Gdef"}, "BaseEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.base_events.BaseEventLoop", "kind": "Gdef"}, "BaseProtocol": {".class": "SymbolTableNode", "cross_ref": "asyncio.protocols.BaseProtocol", "kind": "Gdef"}, "BaseTransport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.BaseTransport", "kind": "Gdef"}, "BoundedSemaphore": {".class": "SymbolTableNode", "cross_ref": "asyncio.locks.BoundedSemaphore", "kind": "Gdef"}, "BrokenBarrierError": {".class": "SymbolTableNode", "cross_ref": "asyncio.exceptions.BrokenBarrierError", "kind": "Gdef"}, "BufferedProtocol": {".class": "SymbolTableNode", "cross_ref": "asyncio.protocols.BufferedProtocol", "kind": "Gdef"}, "CancelledError": {".class": "SymbolTableNode", "cross_ref": "asyncio.exceptions.CancelledError", "kind": "Gdef"}, "Condition": {".class": "SymbolTableNode", "cross_ref": "asyncio.locks.Condition", "kind": "Gdef"}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DatagramProtocol": {".class": "SymbolTableNode", "cross_ref": "asyncio.protocols.DatagramProtocol", "kind": "Gdef"}, "DatagramTransport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.DatagramTransport", "kind": "Gdef"}, "DefaultEventLoopPolicy": {".class": "SymbolTableNode", "cross_ref": "asyncio.windows_events.DefaultEventLoopPolicy", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "asyncio.locks.Event", "kind": "Gdef"}, "FIRST_COMPLETED": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.FIRST_COMPLETED", "kind": "Gdef"}, "FIRST_EXCEPTION": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.FIRST_EXCEPTION", "kind": "Gdef"}, "Future": {".class": "SymbolTableNode", "cross_ref": "_asyncio.Future", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Handle": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.Handle", "kind": "Gdef"}, "IncompleteReadError": {".class": "SymbolTableNode", "cross_ref": "asyncio.exceptions.IncompleteReadError", "kind": "Gdef"}, "InvalidStateError": {".class": "SymbolTableNode", "cross_ref": "asyncio.exceptions.InvalidStateError", "kind": "Gdef"}, "IocpProactor": {".class": "SymbolTableNode", "cross_ref": "asyncio.windows_events.IocpProactor", "kind": "Gdef"}, "LifoQueue": {".class": "SymbolTableNode", "cross_ref": "asyncio.queues.LifoQueue", "kind": "Gdef"}, "LimitOverrunError": {".class": "SymbolTableNode", "cross_ref": "asyncio.exceptions.LimitOverrunError", "kind": "Gdef"}, "Lock": {".class": "SymbolTableNode", "cross_ref": "asyncio.locks.Lock", "kind": "Gdef"}, "PriorityQueue": {".class": "SymbolTableNode", "cross_ref": "asyncio.queues.PriorityQueue", "kind": "Gdef"}, "ProactorEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.windows_events.ProactorEventLoop", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "asyncio.protocols.Protocol", "kind": "Gdef"}, "Queue": {".class": "SymbolTableNode", "cross_ref": "asyncio.queues.Queue", "kind": "Gdef"}, "QueueEmpty": {".class": "SymbolTableNode", "cross_ref": "asyncio.queues.QueueEmpty", "kind": "Gdef"}, "QueueFull": {".class": "SymbolTableNode", "cross_ref": "asyncio.queues.QueueFull", "kind": "Gdef"}, "ReadTransport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.ReadTransport", "kind": "Gdef"}, "Runner": {".class": "SymbolTableNode", "cross_ref": "asyncio.runners.Runner", "kind": "Gdef"}, "SelectorEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.windows_events.SelectorEventLoop", "kind": "Gdef"}, "Semaphore": {".class": "SymbolTableNode", "cross_ref": "asyncio.locks.Semaphore", "kind": "Gdef"}, "SendfileNotAvailableError": {".class": "SymbolTableNode", "cross_ref": "asyncio.exceptions.SendfileNotAvailableError", "kind": "Gdef"}, "Server": {".class": "SymbolTableNode", "cross_ref": "asyncio.base_events.Server", "kind": "Gdef"}, "StreamReader": {".class": "SymbolTableNode", "cross_ref": "asyncio.streams.StreamReader", "kind": "Gdef"}, "StreamReaderProtocol": {".class": "SymbolTableNode", "cross_ref": "asyncio.streams.StreamReaderProtocol", "kind": "Gdef"}, "StreamWriter": {".class": "SymbolTableNode", "cross_ref": "asyncio.streams.StreamWriter", "kind": "Gdef"}, "SubprocessProtocol": {".class": "SymbolTableNode", "cross_ref": "asyncio.protocols.SubprocessProtocol", "kind": "Gdef"}, "SubprocessTransport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.SubprocessTransport", "kind": "Gdef"}, "Task": {".class": "SymbolTableNode", "cross_ref": "_asyncio.Task", "kind": "Gdef"}, "TaskGroup": {".class": "SymbolTableNode", "cross_ref": "asyncio.taskgroups.TaskGroup", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "asyncio.timeouts.Timeout", "kind": "Gdef"}, "TimeoutError": {".class": "SymbolTableNode", "cross_ref": "builtins.TimeoutError", "kind": "Gdef"}, "TimerHandle": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.TimerHandle", "kind": "Gdef"}, "Transport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.Transport", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WindowsProactorEventLoopPolicy": {".class": "SymbolTableNode", "cross_ref": "asyncio.windows_events.WindowsProactorEventLoopPolicy", "kind": "Gdef"}, "WindowsSelectorEventLoopPolicy": {".class": "SymbolTableNode", "cross_ref": "asyncio.windows_events.WindowsSelectorEventLoopPolicy", "kind": "Gdef"}, "WriteTransport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.WriteTransport", "kind": "Gdef"}, "_AwaitableLike": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio._T_co", "id": 1, "name": "_T_co", "namespace": "asyncio._AwaitableLike", "upper_bound": "builtins.object", "values": [], "variance": 1}], "column": 4, "fullname": "asyncio._AwaitableLike", "line": 1337, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio._T_co", "id": 1, "name": "_T_co", "namespace": "asyncio._AwaitableLike", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Awaitable"}}}, "_CoroutineLike": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio._T_co", "id": 1, "name": "_T_co", "namespace": "asyncio._CoroutineLike", "upper_bound": "builtins.object", "values": [], "variance": 1}], "column": 4, "fullname": "asyncio._CoroutineLike", "line": 1338, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio._T_co", "id": 1, "name": "_T_co", "namespace": "asyncio._CoroutineLike", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio._T_co", "name": "_T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_enter_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._enter_task", "kind": "Gdef"}, "_get_running_loop": {".class": "SymbolTableNode", "cross_ref": "_asyncio._get_running_loop", "kind": "Gdef"}, "_leave_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._leave_task", "kind": "Gdef"}, "_register_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._register_task", "kind": "Gdef"}, "_set_running_loop": {".class": "SymbolTableNode", "cross_ref": "_asyncio._set_running_loop", "kind": "Gdef"}, "_unregister_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._unregister_task", "kind": "Gdef"}, "all_tasks": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.all_tasks", "kind": "Gdef"}, "as_completed": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.as_completed", "kind": "Gdef"}, "create_eager_task_factory": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.create_eager_task_factory", "kind": "Gdef"}, "create_subprocess_exec": {".class": "SymbolTableNode", "cross_ref": "asyncio.subprocess.create_subprocess_exec", "kind": "Gdef"}, "create_subprocess_shell": {".class": "SymbolTableNode", "cross_ref": "asyncio.subprocess.create_subprocess_shell", "kind": "Gdef"}, "create_task": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.create_task", "kind": "Gdef"}, "current_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio.current_task", "kind": "Gdef"}, "eager_task_factory": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.eager_task_factory", "kind": "Gdef"}, "ensure_future": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.ensure_future", "kind": "Gdef"}, "gather": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.gather", "kind": "Gdef"}, "get_child_watcher": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.get_child_watcher", "kind": "Gdef"}, "get_event_loop": {".class": "SymbolTableNode", "cross_ref": "_asyncio.get_event_loop", "kind": "Gdef"}, "get_event_loop_policy": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.get_event_loop_policy", "kind": "Gdef"}, "get_running_loop": {".class": "SymbolTableNode", "cross_ref": "_asyncio.get_running_loop", "kind": "Gdef"}, "iscoroutine": {".class": "SymbolTableNode", "cross_ref": "asyncio.coroutines.iscoroutine", "kind": "Gdef"}, "iscoroutinefunction": {".class": "SymbolTableNode", "cross_ref": "asyncio.coroutines.iscoroutinefunction", "kind": "Gdef"}, "isfuture": {".class": "SymbolTableNode", "cross_ref": "asyncio.futures.isfuture", "kind": "Gdef"}, "new_event_loop": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.new_event_loop", "kind": "Gdef"}, "open_connection": {".class": "SymbolTableNode", "cross_ref": "asyncio.streams.open_connection", "kind": "Gdef"}, "run": {".class": "SymbolTableNode", "cross_ref": "asyncio.runners.run", "kind": "Gdef"}, "run_coroutine_threadsafe": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.run_coroutine_threadsafe", "kind": "Gdef"}, "set_child_watcher": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.set_child_watcher", "kind": "Gdef"}, "set_event_loop": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.set_event_loop", "kind": "Gdef"}, "set_event_loop_policy": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.set_event_loop_policy", "kind": "Gdef"}, "shield": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.shield", "kind": "Gdef"}, "sleep": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.sleep", "kind": "Gdef"}, "start_server": {".class": "SymbolTableNode", "cross_ref": "asyncio.streams.start_server", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timeout": {".class": "SymbolTableNode", "cross_ref": "asyncio.timeouts.timeout", "kind": "Gdef"}, "timeout_at": {".class": "SymbolTableNode", "cross_ref": "asyncio.timeouts.timeout_at", "kind": "Gdef"}, "to_thread": {".class": "SymbolTableNode", "cross_ref": "asyncio.threads.to_thread", "kind": "Gdef"}, "wait": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.wait", "kind": "Gdef"}, "wait_for": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.wait_for", "kind": "Gdef"}, "wrap_future": {".class": "SymbolTableNode", "cross_ref": "asyncio.futures.wrap_future", "kind": "Gdef"}}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\asyncio\\__init__.pyi"}