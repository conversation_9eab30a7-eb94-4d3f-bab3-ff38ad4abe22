{".class": "MypyFile", "_fullname": "asyncio.tasks", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ALL_COMPLETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.tasks.ALL_COMPLETED", "name": "ALL_COMPLETED", "type": "builtins.str"}}, "AbstractEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.AbstractEventLoop", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Context": {".class": "SymbolTableNode", "cross_ref": "_contextvars.Context", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FIRST_COMPLETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.tasks.FIRST_COMPLETED", "name": "FIRST_COMPLETED", "type": "builtins.str"}}, "FIRST_EXCEPTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.tasks.FIRST_EXCEPTION", "name": "FIRST_EXCEPTION", "type": "builtins.str"}}, "Future": {".class": "SymbolTableNode", "cross_ref": "_asyncio.Future", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Task": {".class": "SymbolTableNode", "cross_ref": "_asyncio.Task", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoroutineLike": {".class": "SymbolTableNode", "cross_ref": "asyncio._CoroutineLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CustomTaskConstructor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.tasks._CustomTaskConstructor", "name": "_CustomTaskConstructor", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._CustomTaskConstructor", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "asyncio.tasks._CustomTaskConstructor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "asyncio.tasks", "mro": ["asyncio.tasks._CustomTaskConstructor", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3], "arg_names": [null, null, "loop", "name", "context", "eager_start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks._CustomTaskConstructor.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3, 3], "arg_names": [null, null, "loop", "name", "context", "eager_start"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._CustomTaskConstructor", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "asyncio.tasks._CustomTaskConstructor"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "asyncio.tasks._TaskCompatibleCoro"}, "asyncio.events.AbstractEventLoop", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_contextvars.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CustomTaskConstructor", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._CustomTaskConstructor", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._CustomTaskConstructor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._CustomTaskConstructor", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "asyncio.tasks._CustomTaskConstructor"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_TaskT_co"], "typeddict_type": null}}, "_EagerTaskFactoryType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.tasks._EagerTaskFactoryType", "name": "_EagerTaskFactoryType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._EagerTaskFactoryType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "asyncio.tasks._EagerTaskFactoryType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "asyncio.tasks", "mro": ["asyncio.tasks._EagerTaskFactoryType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "loop", "coro", "name", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks._EagerTaskFactoryType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "loop", "coro", "name", "context"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._EagerTaskFactoryType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "asyncio.tasks._EagerTaskFactoryType"}, "asyncio.events.AbstractEventLoop", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "asyncio.tasks._TaskCompatibleCoro"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_contextvars.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _EagerTaskFactoryType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._EagerTaskFactoryType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._EagerTaskFactoryType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": 1, "name": "_TaskT_co", "namespace": "asyncio.tasks._EagerTaskFactoryType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "asyncio.tasks._EagerTaskFactoryType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_TaskT_co"], "typeddict_type": null}}, "_FT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "name": "_FT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}}, "_FutureLike": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": 1, "name": "_T", "namespace": "asyncio.tasks._FutureLike", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 4, "fullname": "asyncio.tasks._FutureLike", "line": 79, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": 1, "name": "_T", "namespace": "asyncio.tasks._FutureLike", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": 1, "name": "_T", "namespace": "asyncio.tasks._FutureLike", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": true}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "name": "_T1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "name": "_T2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T3": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "name": "_T3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "name": "_T4", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T5": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "name": "_T5", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T6": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "name": "_T6", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T_co", "name": "_T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "_TaskCompatibleCoro": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T_co", "id": 1, "name": "_T_co", "namespace": "asyncio.tasks._TaskCompatibleCoro", "upper_bound": "builtins.object", "values": [], "variance": 1}], "column": 4, "fullname": "asyncio.tasks._TaskCompatibleCoro", "line": 407, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T_co", "id": 1, "name": "_T_co", "namespace": "asyncio.tasks._TaskCompatibleCoro", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}}}, "_TaskT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "name": "_TaskT_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}}, "_TaskYieldType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "asyncio.tasks._TaskYieldType", "line": 82, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.tasks.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.tasks.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.tasks.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.tasks.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.tasks.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.tasks.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.tasks.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_enter_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._enter_task", "kind": "Gdef"}, "_leave_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._leave_task", "kind": "Gdef"}, "_register_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._register_task", "kind": "Gdef"}, "_unregister_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio._unregister_task", "kind": "Gdef"}, "all_tasks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks.all_tasks", "name": "all_tasks", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["loop"], "arg_types": [{".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_tasks", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_completed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["fs", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks.as_completed", "name": "as_completed", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["fs", "timeout"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.as_completed", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_completed", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.as_completed", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Future"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.as_completed", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "concurrent": {".class": "SymbolTableNode", "cross_ref": "concurrent", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_eager_task_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["custom_task_constructor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks.create_eager_task_factory", "name": "create_eager_task_factory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["custom_task_constructor"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": -1, "name": "_TaskT_co", "namespace": "asyncio.tasks.create_eager_task_factory", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "asyncio.tasks._CustomTaskConstructor"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_eager_task_factory", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": -1, "name": "_TaskT_co", "namespace": "asyncio.tasks.create_eager_task_factory", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "asyncio.tasks._EagerTaskFactoryType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._TaskT_co", "id": -1, "name": "_TaskT_co", "namespace": "asyncio.tasks.create_eager_task_factory", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "values": [], "variance": 1}]}}}, "create_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["coro", "name", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks.create_task", "name": "create_task", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["coro", "name", "context"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.create_task", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio._CoroutineLike"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_contextvars.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_task", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.create_task", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.create_task", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "current_task": {".class": "SymbolTableNode", "cross_ref": "_asyncio.current_task", "kind": "Gdef"}, "eager_task_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["loop", "coro", "name", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks.eager_task_factory", "name": "eager_task_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["loop", "coro", "name", "context"], "arg_types": [{".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T_co", "id": -1, "name": "_T_co", "namespace": "asyncio.tasks.eager_task_factory", "upper_bound": "builtins.object", "values": [], "variance": 1}], "type_ref": "asyncio.tasks._TaskCompatibleCoro"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_contextvars.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eager_task_factory", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T_co", "id": -1, "name": "_T_co", "namespace": "asyncio.tasks.eager_task_factory", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T_co", "id": -1, "name": "_T_co", "namespace": "asyncio.tasks.eager_task_factory", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}}, "ensure_future": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "asyncio.tasks.ensure_future", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.ensure_future", "name": "ensure_future", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_future", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.ensure_future", "name": "ensure_future", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_future", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.ensure_future", "name": "ensure_future", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_future", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.ensure_future", "name": "ensure_future", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_future", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_future", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.ensure_future#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["coro_or_future", "loop"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_future", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.ensure_future", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "gather": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "asyncio.tasks.gather", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": [null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": [null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": [null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": [null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": [null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": [null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": [null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": [null, null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": [null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": [null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5], "arg_names": ["coros_or_futures", "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["coros_or_futures", "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["coros_or_futures", "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": [null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": [null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": [null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": [null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": [null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": [null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": [null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": [null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": [null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 3], "arg_names": [null, null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 3], "arg_names": [null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 3], "arg_names": [null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 3], "arg_names": ["coros_or_futures", "return_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [2, 3], "arg_names": ["coros_or_futures", "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.gather", "name": "gather", "type": {".class": "CallableType", "arg_kinds": [2, 3], "arg_names": ["coros_or_futures", "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5], "arg_names": [null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": [null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": [null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#4", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 5], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#5", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["coros_or_futures", "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather#6", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": [null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#7", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": [null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#8", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": [null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#9", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 3], "arg_names": [null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#10", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#11", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 3], "arg_names": [null, null, null, null, null, null, "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T1", "id": -1, "name": "_T1", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T2", "id": -2, "name": "_T2", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T3", "id": -3, "name": "_T3", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T4", "id": -4, "name": "_T4", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T5", "id": -5, "name": "_T5", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T6", "id": -6, "name": "_T6", "namespace": "asyncio.tasks.gather#12", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [2, 3], "arg_names": ["coros_or_futures", "return_exceptions"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.BaseException"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.gather", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "run_coroutine_threadsafe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["coro", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks.run_coroutine_threadsafe", "name": "run_coroutine_threadsafe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["coro", "loop"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.run_coroutine_threadsafe", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, "asyncio.events.AbstractEventLoop"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_coroutine_threadsafe", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.run_coroutine_threadsafe", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.run_coroutine_threadsafe", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "shield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.tasks.shield", "name": "shield", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.shield", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shield", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.shield", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.shield", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "sleep": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "asyncio.tasks.sleep", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "asyncio.tasks.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["delay"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["delay"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["delay", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "asyncio.tasks.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["delay", "result"], "arg_types": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["delay", "result"], "arg_types": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["delay"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["delay", "result"], "arg_types": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.sleep", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "wait": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "asyncio.tasks.wait", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "asyncio.tasks.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "asyncio.tasks.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "asyncio.tasks.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._FT", "id": -1, "name": "_FT", "namespace": "asyncio.tasks.wait#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["fs", "timeout", "return_when"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "wait_for": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fut", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "asyncio.tasks.wait_for", "name": "wait_for", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fut", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait_for", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "asyncio.tasks._FutureLike"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_for", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait_for", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.tasks._T", "id": -1, "name": "_T", "namespace": "asyncio.tasks.wait_for", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\asyncio\\tasks.pyi"}