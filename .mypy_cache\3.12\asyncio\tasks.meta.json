{"data_mtime": 1750533063, "dep_lines": [1, 10, 15, 16, 1, 2, 3, 11, 12, 14, 19, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "collections.abc", "asyncio.events", "asyncio.futures", "concurrent", "sys", "_asyncio", "typing", "typing_extensions", "asyncio", "<PERSON><PERSON><PERSON>", "builtins", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "concurrent.futures._base", "types"], "hash": "dd60b1a4c3c9ed245b1b78ff09e2eef19702e9b5", "id": "asyncio.tasks", "ignore_all": true, "interface_hash": "f6c2e8795c8c5f53775a35568e35741a8124fe5f", "mtime": 1750363990, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\asyncio\\tasks.pyi", "plugin_data": null, "size": 16667, "suppressed": [], "version_id": "1.15.0"}