{"data_mtime": 1750533062, "dep_lines": [1, 2, 3, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30], "dependencies": ["codecs", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "823e8c8b9e753dc9ca7bbd8f427ee8608af57ead", "id": "dotenv.parser", "ignore_all": true, "interface_hash": "ffc1e30605090cfb21459e3440367f5dec94e64a", "mtime": 1750366405, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\dotenv\\parser.py", "plugin_data": null, "size": 5186, "suppressed": [], "version_id": "1.15.0"}