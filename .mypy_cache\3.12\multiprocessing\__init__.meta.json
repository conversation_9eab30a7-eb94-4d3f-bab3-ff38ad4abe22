{"data_mtime": 1750533063, "dep_lines": [1, 1, 9, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["multiprocessing.context", "multiprocessing.reduction", "multiprocessing.process", "multiprocessing.queues", "multiprocessing.spawn", "builtins", "_ctypes", "_frozen_importlib", "abc", "ctypes", "logging", "multiprocessing.connection", "multiprocessing.managers", "multiprocessing.pool", "multiprocessing.sharedctypes", "multiprocessing.synchronize", "threading", "typing"], "hash": "f5942c6dd5716d8c7e57f310eb2541a24f7f8bed", "id": "multiprocessing", "ignore_all": true, "interface_hash": "49294c3b0d1d95527ff8817cfcbac5cdd5bdea4a", "mtime": 1750363990, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\multiprocessing\\__init__.pyi", "plugin_data": null, "size": 3132, "suppressed": [], "version_id": "1.15.0"}