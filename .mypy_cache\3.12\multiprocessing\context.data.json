{".class": "MypyFile", "_fullname": "multiprocessing.context", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AuthenticationError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.context.ProcessError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.AuthenticationError", "name": "AuthenticationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.AuthenticationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.AuthenticationError", "multiprocessing.context.ProcessError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.AuthenticationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.AuthenticationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseContext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.BaseContext", "name": "BaseContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.BaseContext", "builtins.object"], "names": {".class": "SymbolTable", "Array": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Array", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": "ctypes.c_char"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": "ctypes.c_char"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Array", "name": "Array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": "ctypes.c_char"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": "multiprocessing.sharedctypes.SynchronizedString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Array#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.SynchronizedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "typecode_or_type", "size_or_initializer", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Array of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "AuthenticationError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "multiprocessing.context.BaseContext.AuthenticationError", "name": "AuthenticationError", "type": {".class": "TypeType", "item": "multiprocessing.context.AuthenticationError"}}}, "Barrier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "parties", "action", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Barrier", "name": "Barrier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "parties", "action", "timeout"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Barrier of BaseContext", "ret_type": "multiprocessing.synchronize.Barrier", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "BoundedSemaphore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.BoundedSemaphore", "name": "BoundedSemaphore", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "value"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BoundedSemaphore of BaseContext", "ret_type": "multiprocessing.synchronize.BoundedSemaphore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "BufferTooShort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "multiprocessing.context.BaseContext.BufferTooShort", "name": "BufferTooShort", "type": {".class": "TypeType", "item": "multiprocessing.context.BufferTooShort"}}}, "Condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Condition", "name": "Condition", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Condition of BaseContext", "ret_type": "multiprocessing.synchronize.Condition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Event", "name": "Event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Event of BaseContext", "ret_type": "multiprocessing.synchronize.Event", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "JoinableQueue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "maxsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.JoinableQueue", "name": "Jo<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "maxsize"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "JoinableQueue of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.queues.JoinableQueue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Lock", "name": "Lock", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Lock of BaseContext", "ret_type": "multiprocessing.synchronize.Lock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Manager", "name": "Manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Manager of BaseContext", "ret_type": "multiprocessing.managers.SyncManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Pipe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "duplex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Pipe", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "duplex"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON> of BaseContext", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.connection.PipeConnection"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.connection.PipeConnection"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "processes", "initializer", "initargs", "maxtasksperchild"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Pool", "name": "Pool", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "processes", "initializer", "initargs", "maxtasksperchild"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Pool of BaseContext", "ret_type": "multiprocessing.pool.Pool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ProcessError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "multiprocessing.context.BaseContext.ProcessError", "name": "ProcessError", "type": {".class": "TypeType", "item": "multiprocessing.context.ProcessError"}}}, "Queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "maxsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Queue", "name": "Queue", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "maxsize"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Queue of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RLock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.RLock", "name": "RLock", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RLock of BaseContext", "ret_type": "multiprocessing.synchronize.RLock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RawArray": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.RawArray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.RawArray", "name": "RawArray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawArray#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typecode_or_type", "size_or_initializer"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawArray of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "RawValue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.RawValue", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue of BaseContext", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue of BaseContext", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.RawValue", "name": "RawValue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue of BaseContext", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.RawValue#0", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "typecode_or_type", "args"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RawValue of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "Semaphore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Semaphore", "name": "Semaphore", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "value"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Semaphore of BaseContext", "ret_type": "multiprocessing.synchronize.Semaphore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "SimpleQueue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.SimpleQueue", "name": "SimpleQueue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SimpleQueue of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.queues.SimpleQueue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "TimeoutError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "multiprocessing.context.BaseContext.TimeoutError", "name": "TimeoutError", "type": {".class": "TypeType", "item": "multiprocessing.context.TimeoutError"}}}, "Value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.Value", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "typecode_or_type", "args", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.Value", "name": "Value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "id": -1, "name": "_T", "namespace": "multiprocessing.context.BaseContext.Value#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#1", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "id": -1, "name": "_CT", "namespace": "multiprocessing.context.BaseContext.Value#2", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "multiprocessing.sharedctypes.Synchronized"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "typecode_or_type", "args", "lock"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_ctypes._CData"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "multiprocessing.context._LockLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Value of BaseContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_check_available": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext._check_available", "name": "_check_available", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_available of BaseContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "active_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.active_children", "name": "active_children", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active_children of BaseContext", "ret_type": {".class": "Instance", "args": ["multiprocessing.process.BaseProcess"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.active_children", "name": "active_children", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active_children of BaseContext", "ret_type": {".class": "Instance", "args": ["multiprocessing.process.BaseProcess"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "allow_connection_pickling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.allow_connection_pickling", "name": "allow_connection_pickling", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allow_connection_pickling of BaseContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cpu_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.cpu_count", "name": "cpu_count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cpu_count of BaseContext", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.current_process", "name": "current_process", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_process of BaseContext", "ret_type": "multiprocessing.process.BaseProcess", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.current_process", "name": "current_process", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_process of BaseContext", "ret_type": "multiprocessing.process.BaseProcess", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "freeze_support": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.freeze_support", "name": "freeze_support", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "freeze_support of BaseContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.get_context", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.get_context", "name": "get_context", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.DefaultContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.get_context", "name": "get_context", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.DefaultContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.get_context", "name": "get_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "LiteralType", "fallback": "builtins.str", "value": "spawn"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.SpawnContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.get_context", "name": "get_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "LiteralType", "fallback": "builtins.str", "value": "spawn"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.SpawnContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.get_context", "name": "get_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.BaseContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.get_context", "name": "get_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.BaseContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.DefaultContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "LiteralType", "fallback": "builtins.str", "value": "spawn"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.SpawnContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of BaseContext", "ret_type": "multiprocessing.context.BaseContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.get_logger", "name": "get_logger", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_logger of BaseContext", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_start_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.get_start_method", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "allow_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.get_start_method", "name": "get_start_method", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "allow_none"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_start_method of BaseContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.get_start_method", "name": "get_start_method", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "allow_none"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_start_method of BaseContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "allow_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.get_start_method", "name": "get_start_method", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "allow_none"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_start_method of BaseContext", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.get_start_method", "name": "get_start_method", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "allow_none"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_start_method of BaseContext", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "allow_none"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_start_method of BaseContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "allow_none"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_start_method of BaseContext", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "log_to_stderr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.log_to_stderr", "name": "log_to_stderr", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "level"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "logging._Level"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_to_stderr of BaseContext", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parent_process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.parent_process", "name": "parent_process", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent_process of BaseContext", "ret_type": {".class": "UnionType", "items": ["multiprocessing.process.BaseProcess", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.parent_process", "name": "parent_process", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent_process of BaseContext", "ret_type": {".class": "UnionType", "items": ["multiprocessing.process.BaseProcess", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reducer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "multiprocessing.context.BaseContext.reducer", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "multiprocessing.context.BaseContext.reducer", "name": "reducer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reducer of BaseContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "multiprocessing.context.BaseContext.reducer", "name": "reducer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reducer of BaseContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "reduction"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "multiprocessing.context.BaseContext.reducer", "name": "reducer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "reduction"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reducer of BaseContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "reducer", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reducer of BaseContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "set_executable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.set_executable", "name": "set_executable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "executable"], "arg_types": ["multiprocessing.context.BaseContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_executable of BaseContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_forkserver_preload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.set_forkserver_preload", "name": "set_forkserver_preload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module_names"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_forkserver_preload of BaseContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_start_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "method", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BaseContext.set_start_method", "name": "set_start_method", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "method", "force"], "arg_types": ["multiprocessing.context.BaseContext", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_start_method of BaseContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.BaseContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.BaseContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseProcess": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.process.BaseProcess", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferTooShort": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.context.ProcessError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.BufferTooShort", "name": "BufferTooShort", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.BufferTooShort", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.BufferTooShort", "multiprocessing.context.ProcessError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.BufferTooShort.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.BufferTooShort", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DefaultContext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.context.BaseContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.DefaultContext", "name": "DefaultContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.DefaultContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.DefaultContext", "multiprocessing.context.BaseContext", "builtins.object"], "names": {".class": "SymbolTable", "Process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "multiprocessing.context.DefaultContext.Process", "name": "Process", "type": {".class": "TypeType", "item": "multiprocessing.context.Process"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.DefaultContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["multiprocessing.context.DefaultContext", "multiprocessing.context.BaseContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefaultContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_all_start_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.DefaultContext.get_all_start_methods", "name": "get_all_start_methods", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.context.DefaultContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_start_methods of DefaultContext", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_start_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "allow_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.DefaultContext.get_start_method", "name": "get_start_method", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "allow_none"], "arg_types": ["multiprocessing.context.DefaultContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_start_method of DefaultContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.DefaultContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.DefaultContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PipeConnection": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.connection.PipeConnection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Process": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.process.BaseProcess"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.Process", "name": "Process", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.Process", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.Process", "multiprocessing.process.BaseProcess", "builtins.object"], "names": {".class": "SymbolTable", "_Popen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["process_obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "multiprocessing.context.Process._Popen", "name": "_<PERSON>n", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["process_obj"], "arg_types": ["multiprocessing.process.BaseProcess"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_Popen of Process", "ret_type": "multiprocessing.context.DefaultContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "multiprocessing.context.Process._Popen", "name": "_<PERSON>n", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["process_obj"], "arg_types": ["multiprocessing.process.BaseProcess"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_Popen of Process", "ret_type": "multiprocessing.context.DefaultContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_start_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.context.Process._start_method", "name": "_start_method", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.Process.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.Process", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProcessError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.ProcessError", "name": "ProcessError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.ProcessError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.ProcessError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.ProcessError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.ProcessError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SpawnContext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.context.BaseContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.SpawnContext", "name": "SpawnContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.SpawnContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.SpawnContext", "multiprocessing.context.BaseContext", "builtins.object"], "names": {".class": "SymbolTable", "Process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "multiprocessing.context.SpawnContext.Process", "name": "Process", "type": {".class": "TypeType", "item": "multiprocessing.context.SpawnProcess"}}}, "_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.context.SpawnContext._name", "name": "_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.SpawnContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.SpawnContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SpawnProcess": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.process.BaseProcess"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.SpawnProcess", "name": "SpawnProcess", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.SpawnProcess", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.SpawnProcess", "multiprocessing.process.BaseProcess", "builtins.object"], "names": {".class": "SymbolTable", "_Popen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["process_obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "multiprocessing.context.SpawnProcess._Popen", "name": "_<PERSON>n", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["process_obj"], "arg_types": ["multiprocessing.process.BaseProcess"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_Popen of SpawnProcess", "ret_type": "multiprocessing.popen_spawn_win32.Popen", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "multiprocessing.context.SpawnProcess._Popen", "name": "_<PERSON>n", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["process_obj"], "arg_types": ["multiprocessing.process.BaseProcess"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_Popen of SpawnProcess", "ret_type": "multiprocessing.popen_spawn_win32.Popen", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_start_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.context.SpawnProcess._start_method", "name": "_start_method", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.SpawnProcess.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.SpawnProcess", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncManager": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.managers.SyncManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Synchronized": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.sharedctypes.Synchronized", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SynchronizedArray": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.sharedctypes.SynchronizedArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SynchronizedString": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.sharedctypes.SynchronizedString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["multiprocessing.context.ProcessError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.context.TimeoutError", "name": "TimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.context.TimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.context", "mro": ["multiprocessing.context.TimeoutError", "multiprocessing.context.ProcessError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context.TimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.context.TimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CData": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CData", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._CT", "name": "_CT", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, "_LockLike": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "multiprocessing.context._LockLike", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["multiprocessing.synchronize.Lock", "multiprocessing.synchronize.RLock"], "uses_pep604_syntax": true}}}, "_LoggingLevel": {".class": "SymbolTableNode", "cross_ref": "logging._Level", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Pool": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.pool.Pool", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SimpleCData": {".class": "SymbolTableNode", "cross_ref": "_ctypes._SimpleCData", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.context._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "multiprocessing.context.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.context.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.context.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.context.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.context.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.context.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.context.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_default_context": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.context._default_context", "name": "_default_context", "type": "multiprocessing.context.DefaultContext"}}, "_force_start_method": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context._force_start_method", "name": "_force_start_method", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["method"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_force_start_method", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_spawning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.assert_spawning", "name": "assert_spawning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_spawning", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "c_char": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_char", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ctypes": {".class": "SymbolTableNode", "cross_ref": "ctypes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_spawning_popen": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.get_spawning_popen", "name": "get_spawning_popen", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_spawning_popen", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "popen_fork": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.popen_fork", "kind": "Gdef", "module_hidden": true, "module_public": false}, "popen_forkserver": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.popen_forkserver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "popen_spawn_posix": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.popen_spawn_posix", "kind": "Gdef", "module_hidden": true, "module_public": false}, "popen_spawn_win32": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.popen_spawn_win32", "kind": "Gdef", "module_hidden": true, "module_public": false}, "queues": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.queues", "kind": "Gdef", "module_hidden": true, "module_public": false}, "set_spawning_popen": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["popen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.context.set_spawning_popen", "name": "set_spawning_popen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["popen"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_spawning_popen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "synchronize": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.synchronize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\multiprocessing\\context.pyi"}