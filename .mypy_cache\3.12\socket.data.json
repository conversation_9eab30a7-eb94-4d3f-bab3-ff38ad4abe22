{".class": "MypyFile", "_fullname": "socket", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AF_APPLETALK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_APPLETALK", "name": "AF_APPLETALK", "type": "socket.AddressFamily"}}, "AF_BLUETOOTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_BLUETOOTH", "name": "AF_BLUETOOTH", "type": "socket.AddressFamily"}}, "AF_DECnet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.AF_DECnet", "name": "AF_DECnet", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 12}}}, "AF_HYPERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_HYPERV", "name": "AF_HYPERV", "type": "socket.AddressFamily"}}, "AF_INET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_INET", "name": "AF_INET", "type": "socket.AddressFamily"}}, "AF_INET6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_INET6", "name": "AF_INET6", "type": "socket.AddressFamily"}}, "AF_IPX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_IPX", "name": "AF_IPX", "type": "socket.AddressFamily"}}, "AF_IRDA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_IRDA", "name": "AF_IRDA", "type": "socket.AddressFamily"}}, "AF_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_LINK", "name": "AF_LINK", "type": "socket.AddressFamily"}}, "AF_SNA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_SNA", "name": "AF_SNA", "type": "socket.AddressFamily"}}, "AF_UNSPEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_UNSPEC", "name": "AF_UNSPEC", "type": "socket.AddressFamily"}}, "AI_ADDRCONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_ADDRCONFIG", "name": "AI_ADDRCONFIG", "type": "socket.AddressInfo"}}, "AI_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_ALL", "name": "AI_ALL", "type": "socket.AddressInfo"}}, "AI_CANONNAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_CANONNAME", "name": "AI_CANONNAME", "type": "socket.AddressInfo"}}, "AI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_NUMERICHOST", "name": "AI_NUMERICHOST", "type": "socket.AddressInfo"}}, "AI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_NUMERICSERV", "name": "AI_NUMERICSERV", "type": "socket.AddressInfo"}}, "AI_PASSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_PASSIVE", "name": "AI_PASSIVE", "type": "socket.AddressInfo"}}, "AI_V4MAPPED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_V4MAPPED", "name": "AI_V4MAPPED", "type": "socket.AddressInfo"}}, "AddressFamily": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.AddressFamily", "name": "AddressFamily", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.AddressFamily", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.AddressFamily", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AF_APPLETALK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_APPLETALK", "name": "AF_APPLETALK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "AF_BLUETOOTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_BLUETOOTH", "name": "AF_BLUETOOTH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "AF_HYPERV": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_HYPERV", "name": "AF_HYPERV", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 34}, "type_ref": "builtins.int"}}}, "AF_INET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_INET", "name": "AF_INET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "AF_INET6": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_INET6", "name": "AF_INET6", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "AF_IPX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_IPX", "name": "AF_IPX", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "AF_IRDA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_IRDA", "name": "AF_IRDA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 23}, "type_ref": "builtins.int"}}}, "AF_LINK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_LINK", "name": "AF_LINK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 33}, "type_ref": "builtins.int"}}}, "AF_SNA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_SNA", "name": "AF_SNA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 22}, "type_ref": "builtins.int"}}}, "AF_UNSPEC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_UNSPEC", "name": "AF_UNSPEC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.AddressFamily.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.AddressFamily", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AddressInfo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.AddressInfo", "name": "AddressInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.AddressInfo", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.AddressInfo", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AI_ADDRCONFIG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_ADDRCONFIG", "name": "AI_ADDRCONFIG", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "AI_ALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_ALL", "name": "AI_ALL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "AI_CANONNAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_CANONNAME", "name": "AI_CANONNAME", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "AI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_NUMERICHOST", "name": "AI_NUMERICHOST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "AI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_NUMERICSERV", "name": "AI_NUMERICSERV", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}, "type_ref": "builtins.int"}}}, "AI_PASSIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_PASSIVE", "name": "AI_PASSIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "AI_V4MAPPED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_V4MAPPED", "name": "AI_V4MAPPED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.AddressInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.AddressInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BDADDR_ANY": {".class": "SymbolTableNode", "cross_ref": "_socket.BDADDR_ANY", "kind": "Gdef"}, "BDADDR_LOCAL": {".class": "SymbolTableNode", "cross_ref": "_socket.BDADDR_LOCAL", "kind": "Gdef"}, "BTPROTO_RFCOMM": {".class": "SymbolTableNode", "cross_ref": "_socket.BTPROTO_RFCOMM", "kind": "Gdef"}, "BufferedRWPair": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedRWPair", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedReader": {".class": "SymbolTableNode", "cross_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedWriter": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedWriter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CAPI": {".class": "SymbolTableNode", "cross_ref": "_socket.CAPI", "kind": "Gdef"}, "EAGAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EAGAIN", "name": "EAGAIN", "type": "builtins.int"}}, "EAI_AGAIN": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_AGAIN", "kind": "Gdef"}, "EAI_BADFLAGS": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_BADFLAGS", "kind": "Gdef"}, "EAI_FAIL": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_FAIL", "kind": "Gdef"}, "EAI_FAMILY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_FAMILY", "kind": "Gdef"}, "EAI_MEMORY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_MEMORY", "kind": "Gdef"}, "EAI_NODATA": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_NODATA", "kind": "Gdef"}, "EAI_NONAME": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_NONAME", "kind": "Gdef"}, "EAI_SERVICE": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SERVICE", "kind": "Gdef"}, "EAI_SOCKTYPE": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SOCKTYPE", "kind": "Gdef"}, "EBADF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EBADF", "name": "EBADF", "type": "builtins.int"}}, "EWOULDBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EWOULDBLOCK", "name": "EWOULDBLOCK", "type": "builtins.int"}}, "HVSOCKET_ADDRESS_FLAG_PASSTHRU": {".class": "SymbolTableNode", "cross_ref": "_socket.HVSOCKET_ADDRESS_FLAG_PASSTHRU", "kind": "Gdef"}, "HVSOCKET_CONNECTED_SUSPEND": {".class": "SymbolTableNode", "cross_ref": "_socket.HVSOCKET_CONNECTED_SUSPEND", "kind": "Gdef"}, "HVSOCKET_CONNECT_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "_socket.HVSOCKET_CONNECT_TIMEOUT", "kind": "Gdef"}, "HVSOCKET_CONNECT_TIMEOUT_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.HVSOCKET_CONNECT_TIMEOUT_MAX", "kind": "Gdef"}, "HV_GUID_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.HV_GUID_BROADCAST", "kind": "Gdef"}, "HV_GUID_CHILDREN": {".class": "SymbolTableNode", "cross_ref": "_socket.HV_GUID_CHILDREN", "kind": "Gdef"}, "HV_GUID_LOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.HV_GUID_LOOPBACK", "kind": "Gdef"}, "HV_GUID_PARENT": {".class": "SymbolTableNode", "cross_ref": "_socket.HV_GUID_PARENT", "kind": "Gdef"}, "HV_GUID_WILDCARD": {".class": "SymbolTableNode", "cross_ref": "_socket.HV_GUID_WILDCARD", "kind": "Gdef"}, "HV_GUID_ZERO": {".class": "SymbolTableNode", "cross_ref": "_socket.HV_GUID_ZERO", "kind": "Gdef"}, "HV_PROTOCOL_RAW": {".class": "SymbolTableNode", "cross_ref": "_socket.HV_PROTOCOL_RAW", "kind": "Gdef"}, "INADDR_ALLHOSTS_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_ALLHOSTS_GROUP", "kind": "Gdef"}, "INADDR_ANY": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_ANY", "kind": "Gdef"}, "INADDR_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_BROADCAST", "kind": "Gdef"}, "INADDR_LOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_LOOPBACK", "kind": "Gdef"}, "INADDR_MAX_LOCAL_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_MAX_LOCAL_GROUP", "kind": "Gdef"}, "INADDR_NONE": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_NONE", "kind": "Gdef"}, "INADDR_UNSPEC_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_UNSPEC_GROUP", "kind": "Gdef"}, "IOBase": {".class": "SymbolTableNode", "cross_ref": "io.IOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPPORT_RESERVED": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPORT_RESERVED", "kind": "Gdef"}, "IPPORT_USERRESERVED": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPORT_USERRESERVED", "kind": "Gdef"}, "IPPROTO_AH": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_AH", "kind": "Gdef"}, "IPPROTO_CBT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_CBT", "kind": "Gdef"}, "IPPROTO_DSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_DSTOPTS", "kind": "Gdef"}, "IPPROTO_EGP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_EGP", "kind": "Gdef"}, "IPPROTO_ESP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ESP", "kind": "Gdef"}, "IPPROTO_FRAGMENT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_FRAGMENT", "kind": "Gdef"}, "IPPROTO_GGP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_GGP", "kind": "Gdef"}, "IPPROTO_HOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_HOPOPTS", "kind": "Gdef"}, "IPPROTO_ICLFXBM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ICLFXBM", "kind": "Gdef"}, "IPPROTO_ICMP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ICMP", "kind": "Gdef"}, "IPPROTO_ICMPV6": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ICMPV6", "kind": "Gdef"}, "IPPROTO_IDP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IDP", "kind": "Gdef"}, "IPPROTO_IGMP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IGMP", "kind": "Gdef"}, "IPPROTO_IGP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IGP", "kind": "Gdef"}, "IPPROTO_IP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IP", "kind": "Gdef"}, "IPPROTO_IPV4": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPV4", "kind": "Gdef"}, "IPPROTO_IPV6": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPV6", "kind": "Gdef"}, "IPPROTO_L2TP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_L2TP", "kind": "Gdef"}, "IPPROTO_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_MAX", "kind": "Gdef"}, "IPPROTO_ND": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ND", "kind": "Gdef"}, "IPPROTO_NONE": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_NONE", "kind": "Gdef"}, "IPPROTO_PGM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_PGM", "kind": "Gdef"}, "IPPROTO_PIM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_PIM", "kind": "Gdef"}, "IPPROTO_PUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_PUP", "kind": "Gdef"}, "IPPROTO_RAW": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_RAW", "kind": "Gdef"}, "IPPROTO_RDP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_RDP", "kind": "Gdef"}, "IPPROTO_ROUTING": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ROUTING", "kind": "Gdef"}, "IPPROTO_SCTP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_SCTP", "kind": "Gdef"}, "IPPROTO_ST": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ST", "kind": "Gdef"}, "IPPROTO_TCP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_TCP", "kind": "Gdef"}, "IPPROTO_UDP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_UDP", "kind": "Gdef"}, "IPV6_CHECKSUM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_CHECKSUM", "kind": "Gdef"}, "IPV6_DONTFRAG": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_DONTFRAG", "kind": "Gdef"}, "IPV6_HOPLIMIT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_HOPLIMIT", "kind": "Gdef"}, "IPV6_HOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_HOPOPTS", "kind": "Gdef"}, "IPV6_JOIN_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_JOIN_GROUP", "kind": "Gdef"}, "IPV6_LEAVE_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_LEAVE_GROUP", "kind": "Gdef"}, "IPV6_MULTICAST_HOPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_HOPS", "kind": "Gdef"}, "IPV6_MULTICAST_IF": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_IF", "kind": "Gdef"}, "IPV6_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_LOOP", "kind": "Gdef"}, "IPV6_PKTINFO": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_PKTINFO", "kind": "Gdef"}, "IPV6_RECVRTHDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVRTHDR", "kind": "Gdef"}, "IPV6_RECVTCLASS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVTCLASS", "kind": "Gdef"}, "IPV6_RTHDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RTHDR", "kind": "Gdef"}, "IPV6_TCLASS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_TCLASS", "kind": "Gdef"}, "IPV6_UNICAST_HOPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_UNICAST_HOPS", "kind": "Gdef"}, "IPV6_V6ONLY": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_V6ONLY", "kind": "Gdef"}, "IP_ADD_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_ADD_MEMBERSHIP", "kind": "Gdef"}, "IP_ADD_SOURCE_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_ADD_SOURCE_MEMBERSHIP", "kind": "Gdef"}, "IP_BLOCK_SOURCE": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_BLOCK_SOURCE", "kind": "Gdef"}, "IP_DROP_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DROP_MEMBERSHIP", "kind": "Gdef"}, "IP_DROP_SOURCE_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DROP_SOURCE_MEMBERSHIP", "kind": "Gdef"}, "IP_HDRINCL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_HDRINCL", "kind": "Gdef"}, "IP_MULTICAST_IF": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_IF", "kind": "Gdef"}, "IP_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_LOOP", "kind": "Gdef"}, "IP_MULTICAST_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_TTL", "kind": "Gdef"}, "IP_OPTIONS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_OPTIONS", "kind": "Gdef"}, "IP_PKTINFO": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_PKTINFO", "kind": "Gdef"}, "IP_RECVDSTADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVDSTADDR", "kind": "Gdef"}, "IP_RECVTOS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVTOS", "kind": "Gdef"}, "IP_TOS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_TOS", "kind": "Gdef"}, "IP_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_TTL", "kind": "Gdef"}, "IP_UNBLOCK_SOURCE": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_UNBLOCK_SOURCE", "kind": "Gdef"}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntFlag": {".class": "SymbolTableNode", "cross_ref": "enum.IntFlag", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MSG_BCAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_BCAST", "name": "MSG_BCAST", "type": "socket.MsgFlag"}}, "MSG_CTRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_CTRUNC", "name": "MSG_CTRUNC", "type": "socket.MsgFlag"}}, "MSG_DONTROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_DONTROUTE", "name": "MSG_DONTROUTE", "type": "socket.MsgFlag"}}, "MSG_ERRQUEUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_ERRQUEUE", "name": "MSG_ERRQUEUE", "type": "socket.MsgFlag"}}, "MSG_MCAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_MCAST", "name": "MSG_MCAST", "type": "socket.MsgFlag"}}, "MSG_OOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_OOB", "name": "MSG_OOB", "type": "socket.MsgFlag"}}, "MSG_PEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_PEEK", "name": "MSG_PEEK", "type": "socket.MsgFlag"}}, "MSG_TRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_TRUNC", "name": "MSG_TRUNC", "type": "socket.MsgFlag"}}, "MSG_WAITALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_WAITALL", "name": "MSG_WAITALL", "type": "socket.MsgFlag"}}, "MsgFlag": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.MsgFlag", "name": "MsgFlag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.MsgFlag", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.MsgFlag", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "MSG_BCAST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_BCAST", "name": "MSG_BCAST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}, "type_ref": "builtins.int"}}}, "MSG_CTRUNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_CTRUNC", "name": "MSG_CTRUNC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "MSG_DONTROUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_DONTROUTE", "name": "MSG_DONTROUTE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "MSG_ERRQUEUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_ERRQUEUE", "name": "MSG_ERRQUEUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}, "type_ref": "builtins.int"}}}, "MSG_MCAST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_MCAST", "name": "MSG_MCAST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2048}, "type_ref": "builtins.int"}}}, "MSG_OOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_OOB", "name": "MSG_OOB", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "MSG_PEEK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_PEEK", "name": "MSG_PEEK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "MSG_TRUNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_TRUNC", "name": "MSG_TRUNC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "MSG_WAITALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_WAITALL", "name": "MSG_WAITALL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.MsgFlag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.MsgFlag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NI_DGRAM": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_DGRAM", "kind": "Gdef"}, "NI_MAXHOST": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_MAXHOST", "kind": "Gdef"}, "NI_MAXSERV": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_MAXSERV", "kind": "Gdef"}, "NI_NAMEREQD": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NAMEREQD", "kind": "Gdef"}, "NI_NOFQDN": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NOFQDN", "kind": "Gdef"}, "NI_NUMERICHOST": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NUMERICHOST", "kind": "Gdef"}, "NI_NUMERICSERV": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NUMERICSERV", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RCVALL_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.RCVALL_MAX", "kind": "Gdef"}, "RCVALL_OFF": {".class": "SymbolTableNode", "cross_ref": "_socket.RCVALL_OFF", "kind": "Gdef"}, "RCVALL_ON": {".class": "SymbolTableNode", "cross_ref": "_socket.RCVALL_ON", "kind": "Gdef"}, "RCVALL_SOCKETLEVELONLY": {".class": "SymbolTableNode", "cross_ref": "_socket.RCVALL_SOCKETLEVELONLY", "kind": "Gdef"}, "RawIOBase": {".class": "SymbolTableNode", "cross_ref": "io.RawIOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHUT_RD": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_RD", "kind": "Gdef"}, "SHUT_RDWR": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_RDWR", "kind": "Gdef"}, "SHUT_WR": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_WR", "kind": "Gdef"}, "SIO_KEEPALIVE_VALS": {".class": "SymbolTableNode", "cross_ref": "_socket.SIO_KEEPALIVE_VALS", "kind": "Gdef"}, "SIO_LOOPBACK_FAST_PATH": {".class": "SymbolTableNode", "cross_ref": "_socket.SIO_LOOPBACK_FAST_PATH", "kind": "Gdef"}, "SIO_RCVALL": {".class": "SymbolTableNode", "cross_ref": "_socket.SIO_RCVALL", "kind": "Gdef"}, "SOCK_DGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_DGRAM", "name": "SOCK_DGRAM", "type": "socket.SocketKind"}}, "SOCK_RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_RAW", "name": "SOCK_RAW", "type": "socket.SocketKind"}}, "SOCK_RDM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_RDM", "name": "SOCK_RDM", "type": "socket.SocketKind"}}, "SOCK_SEQPACKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_SEQPACKET", "name": "SOCK_SEQPACKET", "type": "socket.SocketKind"}}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_STREAM", "name": "SOCK_STREAM", "type": "socket.SocketKind"}}, "SOL_IP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_IP", "kind": "Gdef"}, "SOL_SOCKET": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_SOCKET", "kind": "Gdef"}, "SOL_TCP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_TCP", "kind": "Gdef"}, "SOL_UDP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_UDP", "kind": "Gdef"}, "SOMAXCONN": {".class": "SymbolTableNode", "cross_ref": "_socket.SOMAXCONN", "kind": "Gdef"}, "SO_ACCEPTCONN": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_ACCEPTCONN", "kind": "Gdef"}, "SO_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_BROADCAST", "kind": "Gdef"}, "SO_DEBUG": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_DEBUG", "kind": "Gdef"}, "SO_DONTROUTE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_DONTROUTE", "kind": "Gdef"}, "SO_ERROR": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_ERROR", "kind": "Gdef"}, "SO_EXCLUSIVEADDRUSE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_EXCLUSIVEADDRUSE", "kind": "Gdef"}, "SO_KEEPALIVE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_KEEPALIVE", "kind": "Gdef"}, "SO_LINGER": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_LINGER", "kind": "Gdef"}, "SO_OOBINLINE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_OOBINLINE", "kind": "Gdef"}, "SO_RCVBUF": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVBUF", "kind": "Gdef"}, "SO_RCVLOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVLOWAT", "kind": "Gdef"}, "SO_RCVTIMEO": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVTIMEO", "kind": "Gdef"}, "SO_REUSEADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_REUSEADDR", "kind": "Gdef"}, "SO_SNDBUF": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDBUF", "kind": "Gdef"}, "SO_SNDLOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDLOWAT", "kind": "Gdef"}, "SO_SNDTIMEO": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDTIMEO", "kind": "Gdef"}, "SO_TYPE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_TYPE", "kind": "Gdef"}, "SO_USELOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_USELOOPBACK", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SocketIO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["io.RawIOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.SocketIO", "name": "SocketIO", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.SocketIO", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "socket", "mro": ["socket.SocketIO", "io.RawIOBase", "_io._RawIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.SocketIO.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "mode"], "arg_types": ["socket.SocketIO", "socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SocketIO", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "socket.SocketIO.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of SocketIO", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.SocketIO.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of SocketIO", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "socket.SocketIO.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of SocketIO", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.SocketIO.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of SocketIO", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.SocketIO.readinto", "name": "readinto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["socket.SocketIO", "_collections_abc.<PERSON><PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readinto of SocketIO", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.SocketIO.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["socket.SocketIO", "_collections_abc.<PERSON><PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of SocketIO", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.SocketIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.SocketIO", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketKind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.SocketKind", "name": "SocketKind", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.SocketKind", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.SocketKind", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "SOCK_DGRAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_DGRAM", "name": "SOCK_DGRAM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "SOCK_RAW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_RAW", "name": "SOCK_RAW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "SOCK_RDM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_RDM", "name": "SOCK_RDM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "SOCK_SEQPACKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_SEQPACKET", "name": "SOCK_SEQPACKET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_STREAM", "name": "SOCK_STREAM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.SocketKind.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.SocketKind", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketType": {".class": "SymbolTableNode", "cross_ref": "_socket.SocketType", "kind": "Gdef"}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TCP_FASTOPEN": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_FASTOPEN", "kind": "Gdef"}, "TCP_KEEPCNT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPCNT", "kind": "Gdef"}, "TCP_KEEPIDLE": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPIDLE", "kind": "Gdef"}, "TCP_KEEPINTVL": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPINTVL", "kind": "Gdef"}, "TCP_MAXSEG": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_MAXSEG", "kind": "Gdef"}, "TCP_NODELAY": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_NODELAY", "kind": "Gdef"}, "TextIOWrapper": {".class": "SymbolTableNode", "cross_ref": "_io.TextIOWrapper", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Address": {".class": "SymbolTableNode", "cross_ref": "_socket._Address", "kind": "Gdef", "module_public": false}, "_RetAddress": {".class": "SymbolTableNode", "cross_ref": "_socket._RetAddress", "kind": "Gdef", "module_public": false}, "_SendableFile": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket._SendableFile", "name": "_SendableFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "socket._SendableFile", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "socket", "mro": ["socket._SendableFile", "builtins.object"], "names": {".class": "SymbolTable", "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket._SendableFile.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["socket._SendableFile", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of _SendableFile", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket._SendableFile.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["socket._SendableFile", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seek of _SendableFile", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket._SendableFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket._SendableFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_socket": {".class": "SymbolTableNode", "cross_ref": "_socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "close": {".class": "SymbolTableNode", "cross_ref": "_socket.close", "kind": "Gdef"}, "create_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["address", "timeout", "source_address", "all_errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["address", "timeout", "source_address", "all_errors"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["address", "family", "backlog", "reuse_port", "dualstack_ipv6"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.create_server", "name": "create_server", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["address", "family", "backlog", "reuse_port", "dualstack_ipv6"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_server", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dup": {".class": "SymbolTableNode", "cross_ref": "_socket.dup", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "socket.error", "line": 1038, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.OSError"}}, "errorTab": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.errorTab", "name": "errorTab", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fromfd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["fd", "family", "type", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.fromfd", "name": "fromfd", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["fd", "family", "type", "proto"], "arg_types": ["typing.SupportsIndex", {".class": "UnionType", "items": ["socket.AddressFamily", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["socket.SocketKind", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromfd", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fromshare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.fromshare", "name": "fromshare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["info"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromshare", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gaierror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.gaierror", "name": "gaier<PERSON>r", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.gaierror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.gaierror", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.gaierror.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.gaierror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getaddrinfo", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["socket.AddressFamily", "socket.SocketKind", "builtins.int", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdefaulttimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.getdefaulttimeout", "kind": "Gdef"}, "getfqdn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.getfqdn", "name": "getfqdn", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfqdn", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyaddr": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyaddr", "kind": "Gdef"}, "gethostbyname": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyname", "kind": "Gdef"}, "gethostbyname_ex": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyname_ex", "kind": "Gdef"}, "gethostname": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostname", "kind": "Gdef"}, "getnameinfo": {".class": "SymbolTableNode", "cross_ref": "_socket.getnameinfo", "kind": "Gdef"}, "getprotobyname": {".class": "SymbolTableNode", "cross_ref": "_socket.getprotobyname", "kind": "Gdef"}, "getservbyname": {".class": "SymbolTableNode", "cross_ref": "_socket.getservbyname", "kind": "Gdef"}, "getservbyport": {".class": "SymbolTableNode", "cross_ref": "_socket.getservbyport", "kind": "Gdef"}, "has_dualstack_ipv6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.has_dualstack_ipv6", "name": "has_dualstack_ipv6", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_dualstack_ipv6", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_ipv6": {".class": "SymbolTableNode", "cross_ref": "_socket.has_ipv6", "kind": "Gdef"}, "herror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.herror", "name": "herror", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.herror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.herror", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.herror.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.herror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "htonl": {".class": "SymbolTableNode", "cross_ref": "_socket.htonl", "kind": "Gdef"}, "htons": {".class": "SymbolTableNode", "cross_ref": "_socket.htons", "kind": "Gdef"}, "if_indextoname": {".class": "SymbolTableNode", "cross_ref": "_socket.if_indextoname", "kind": "Gdef"}, "if_nameindex": {".class": "SymbolTableNode", "cross_ref": "_socket.if_nameindex", "kind": "Gdef"}, "if_nametoindex": {".class": "SymbolTableNode", "cross_ref": "_socket.if_nametoindex", "kind": "Gdef"}, "inet_aton": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_aton", "kind": "Gdef"}, "inet_ntoa": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_ntoa", "kind": "Gdef"}, "inet_ntop": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_ntop", "kind": "Gdef"}, "inet_pton": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_pton", "kind": "Gdef"}, "ntohl": {".class": "SymbolTableNode", "cross_ref": "_socket.ntohl", "kind": "Gdef"}, "ntohs": {".class": "SymbolTableNode", "cross_ref": "_socket.ntohs", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setdefaulttimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.setdefaulttimeout", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_socket.socket"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.socket", "name": "socket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.socket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.socket", "_socket.socket", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of socket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["socket.socket", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["socket.AddressFamily", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["socket.SocketKind", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.accept", "name": "accept", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept of socket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["socket.socket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.dup", "name": "dup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dup of socket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}]}}}, "family": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "socket.socket.family", "name": "family", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "socket.AddressFamily", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.socket.family", "name": "family", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "socket.AddressFamily", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_inheritable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.get_inheritable", "name": "get_inheritable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_inheritable of socket", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "socket.socket.makefile", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedRWPair", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedRWPair", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedRWPair", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sendfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "file", "offset", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.sendfile", "name": "sendfile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "file", "offset", "count"], "arg_types": ["socket.socket", "socket._SendableFile", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendfile of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_inheritable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inheritable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.set_inheritable", "name": "set_inheritable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inheritable"], "arg_types": ["socket.socket", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_inheritable of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "socket.socket.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "socket.SocketKind", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.socket.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "socket.SocketKind", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "socketpair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["family", "type", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socketpair", "name": "socketpair", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["family", "type", "proto"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "socketpair", "ret_type": {".class": "TupleType", "implicit": false, "items": ["socket.socket", "socket.socket"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "socket.timeout", "line": 1044, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.TimeoutError"}}}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\socket.pyi"}