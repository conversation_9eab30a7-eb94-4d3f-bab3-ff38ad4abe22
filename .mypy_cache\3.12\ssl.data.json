{".class": "MypyFile", "_fullname": "ssl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ALERT_DESCRIPTION_ACCESS_DENIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_ACCESS_DENIED", "name": "ALERT_DESCRIPTION_ACCESS_DENIED", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_BAD_RECORD_MAC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_BAD_RECORD_MAC", "name": "ALERT_DESCRIPTION_BAD_RECORD_MAC", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_CERTIFICATE_EXPIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_CERTIFICATE_EXPIRED", "name": "ALERT_DESCRIPTION_CERTIFICATE_EXPIRED", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_CERTIFICATE_REVOKED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_CERTIFICATE_REVOKED", "name": "ALERT_DESCRIPTION_CERTIFICATE_REVOKED", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN", "name": "ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE", "name": "ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_CLOSE_NOTIFY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_CLOSE_NOTIFY", "name": "ALERT_DESCRIPTION_CLOSE_NOTIFY", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_DECODE_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_DECODE_ERROR", "name": "ALERT_DESCRIPTION_DECODE_ERROR", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_DECOMPRESSION_FAILURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_DECOMPRESSION_FAILURE", "name": "ALERT_DESCRIPTION_DECOMPRESSION_FAILURE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_DECRYPT_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_DECRYPT_ERROR", "name": "ALERT_DESCRIPTION_DECRYPT_ERROR", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_HANDSHAKE_FAILURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE", "name": "ALERT_DESCRIPTION_HANDSHAKE_FAILURE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_ILLEGAL_PARAMETER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_ILLEGAL_PARAMETER", "name": "ALERT_DESCRIPTION_ILLEGAL_PARAMETER", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_INSUFFICIENT_SECURITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_INSUFFICIENT_SECURITY", "name": "ALERT_DESCRIPTION_INSUFFICIENT_SECURITY", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_INTERNAL_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_INTERNAL_ERROR", "name": "ALERT_DESCRIPTION_INTERNAL_ERROR", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_NO_RENEGOTIATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_NO_RENEGOTIATION", "name": "ALERT_DESCRIPTION_NO_RENEGOTIATION", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_PROTOCOL_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_PROTOCOL_VERSION", "name": "ALERT_DESCRIPTION_PROTOCOL_VERSION", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_RECORD_OVERFLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_RECORD_OVERFLOW", "name": "ALERT_DESCRIPTION_RECORD_OVERFLOW", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_UNEXPECTED_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_UNEXPECTED_MESSAGE", "name": "ALERT_DESCRIPTION_UNEXPECTED_MESSAGE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_UNKNOWN_CA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_UNKNOWN_CA", "name": "ALERT_DESCRIPTION_UNKNOWN_CA", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY", "name": "ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_UNRECOGNIZED_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_UNRECOGNIZED_NAME", "name": "ALERT_DESCRIPTION_UNRECOGNIZED_NAME", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE", "name": "ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION", "name": "ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION", "type": "ssl.AlertDes<PERSON>"}}, "ALERT_DESCRIPTION_USER_CANCELLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.ALERT_DESCRIPTION_USER_CANCELLED", "name": "ALERT_DESCRIPTION_USER_CANCELLED", "type": "ssl.AlertDes<PERSON>"}}, "AlertDescription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.AlertDes<PERSON>", "name": "AlertDescription", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl.AlertDes<PERSON>", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl.AlertDes<PERSON>", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ALERT_DESCRIPTION_ACCESS_DENIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_ACCESS_DENIED", "name": "ALERT_DESCRIPTION_ACCESS_DENIED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 49}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_BAD_CERTIFICATE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 42}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 114}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 113}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_BAD_RECORD_MAC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_BAD_RECORD_MAC", "name": "ALERT_DESCRIPTION_BAD_RECORD_MAC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 20}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_CERTIFICATE_EXPIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_CERTIFICATE_EXPIRED", "name": "ALERT_DESCRIPTION_CERTIFICATE_EXPIRED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 45}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_CERTIFICATE_REVOKED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_CERTIFICATE_REVOKED", "name": "ALERT_DESCRIPTION_CERTIFICATE_REVOKED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 44}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN", "name": "ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 46}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE", "name": "ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 111}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_CLOSE_NOTIFY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_CLOSE_NOTIFY", "name": "ALERT_DESCRIPTION_CLOSE_NOTIFY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_DECODE_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_DECODE_ERROR", "name": "ALERT_DESCRIPTION_DECODE_ERROR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 50}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_DECOMPRESSION_FAILURE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_DECOMPRESSION_FAILURE", "name": "ALERT_DESCRIPTION_DECOMPRESSION_FAILURE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 30}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_DECRYPT_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_DECRYPT_ERROR", "name": "ALERT_DESCRIPTION_DECRYPT_ERROR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 51}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_HANDSHAKE_FAILURE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_HAN<PERSON><PERSON><PERSON>_FAILURE", "name": "ALERT_DESCRIPTION_HANDSHAKE_FAILURE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 40}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_ILLEGAL_PARAMETER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_ILLEGAL_PARAMETER", "name": "ALERT_DESCRIPTION_ILLEGAL_PARAMETER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 47}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_INSUFFICIENT_SECURITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_INSUFFICIENT_SECURITY", "name": "ALERT_DESCRIPTION_INSUFFICIENT_SECURITY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 71}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_INTERNAL_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_INTERNAL_ERROR", "name": "ALERT_DESCRIPTION_INTERNAL_ERROR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 80}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_NO_RENEGOTIATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_NO_RENEGOTIATION", "name": "ALERT_DESCRIPTION_NO_RENEGOTIATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 100}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_PROTOCOL_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_PROTOCOL_VERSION", "name": "ALERT_DESCRIPTION_PROTOCOL_VERSION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 70}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_RECORD_OVERFLOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_RECORD_OVERFLOW", "name": "ALERT_DESCRIPTION_RECORD_OVERFLOW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 22}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_UNEXPECTED_MESSAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_UNEXPECTED_MESSAGE", "name": "ALERT_DESCRIPTION_UNEXPECTED_MESSAGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_UNKNOWN_CA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_UNKNOWN_CA", "name": "ALERT_DESCRIPTION_UNKNOWN_CA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 48}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY", "name": "ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 115}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_UNRECOGNIZED_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_UNRECOGNIZED_NAME", "name": "ALERT_DESCRIPTION_UNRECOGNIZED_NAME", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 112}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE", "name": "ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 43}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION", "name": "ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 110}, "type_ref": "builtins.int"}}}, "ALERT_DESCRIPTION_USER_CANCELLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.AlertDescription.ALERT_DESCRIPTION_USER_CANCELLED", "name": "ALERT_DESCRIPTION_USER_CANCELLED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 90}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.AlertDescription.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.AlertDes<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CERT_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.CERT_NONE", "name": "CERT_NONE", "type": "ssl.VerifyMode"}}, "CERT_OPTIONAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.CERT_OPTIONAL", "name": "CERT_OPTIONAL", "type": "ssl.VerifyMode"}}, "CERT_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.CERT_REQUIRED", "name": "CERT_REQUIRED", "type": "ssl.VerifyMode"}}, "CHANNEL_BINDING_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.CHANNEL_BINDING_TYPES", "name": "CHANNEL_BINDING_TYPES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CertificateError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ssl.CertificateError", "line": 80, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ssl.SSLCertVerificationError"}}, "DER_cert_to_PEM_cert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["der_cert_bytes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.DER_cert_to_PEM_cert", "name": "DER_cert_to_PEM_cert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["der_cert_bytes"], "arg_types": ["_collections_abc.<PERSON><PERSON>er"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DER_cert_to_PEM_cert", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DefaultVerifyPaths": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.DefaultVerifyPaths", "name": "DefaultVerifyPaths", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "ssl.DefaultVerifyPaths", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["cafile", "<PERSON><PERSON>", "openssl_cafile_env", "openssl_cafile", "openssl_capath_env", "openssl_capath"]}}, "module_name": "ssl", "mro": ["ssl.DefaultVerifyPaths", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cafile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "openssl_cafile_env"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "openssl_cafile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "openssl_capath_env"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "openssl_capath"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "cafile", "<PERSON><PERSON>", "openssl_cafile_env", "openssl_cafile", "openssl_capath_env", "openssl_capath"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "ssl.DefaultVerifyPaths.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "cafile", "<PERSON><PERSON>", "openssl_cafile_env", "openssl_cafile", "openssl_capath_env", "openssl_capath"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of DefaultVerifyPaths", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.DefaultVerifyPaths._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of DefaultVerifyPaths", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.DefaultVerifyPaths._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.DefaultVerifyPaths._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.DefaultVerifyPaths._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ssl.DefaultVerifyPaths._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DefaultVerifyPaths", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "ssl.DefaultVerifyPaths._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of DefaultVerifyPaths", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "cafile", "<PERSON><PERSON>", "openssl_cafile_env", "openssl_cafile", "openssl_capath_env", "openssl_capath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.DefaultVerifyPaths._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "cafile", "<PERSON><PERSON>", "openssl_cafile_env", "openssl_cafile", "openssl_capath_env", "openssl_capath"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of DefaultVerifyPaths", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths._NT", "id": -1, "name": "_NT", "namespace": "ssl.DefaultVerifyPaths._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.DefaultVerifyPaths._source", "name": "_source", "type": "builtins.str"}}, "cafile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.cafile", "name": "cafile", "type": "builtins.str"}}, "cafile-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl.DefaultVerifyPaths.cafile", "kind": "<PERSON><PERSON><PERSON>"}, "capath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.capath", "name": "<PERSON><PERSON>", "type": "builtins.str"}}, "capath-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl.DefaultVerifyPaths.capath", "kind": "<PERSON><PERSON><PERSON>"}, "openssl_cafile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.openssl_cafile", "name": "openssl_cafile", "type": "builtins.str"}}, "openssl_cafile-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl.DefaultVerifyPaths.openssl_cafile", "kind": "<PERSON><PERSON><PERSON>"}, "openssl_cafile_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.openssl_cafile_env", "name": "openssl_cafile_env", "type": "builtins.str"}}, "openssl_cafile_env-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl.DefaultVerifyPaths.openssl_cafile_env", "kind": "<PERSON><PERSON><PERSON>"}, "openssl_capath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.openssl_capath", "name": "openssl_capath", "type": "builtins.str"}}, "openssl_capath-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl.DefaultVerifyPaths.openssl_capath", "kind": "<PERSON><PERSON><PERSON>"}, "openssl_capath_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl.DefaultVerifyPaths.openssl_capath_env", "name": "openssl_capath_env", "type": "builtins.str"}}, "openssl_capath_env-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl.DefaultVerifyPaths.openssl_capath_env", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.DefaultVerifyPaths.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl.DefaultVerifyPaths"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "HAS_ALPN": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_ALPN", "kind": "Gdef"}, "HAS_ECDH": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_ECDH", "kind": "Gdef"}, "HAS_NEVER_CHECK_COMMON_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.HAS_NEVER_CHECK_COMMON_NAME", "name": "HAS_NEVER_CHECK_COMMON_NAME", "type": "builtins.bool"}}, "HAS_NPN": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_NPN", "kind": "Gdef"}, "HAS_SNI": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_SNI", "kind": "Gdef"}, "HAS_SSLv2": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_SSLv2", "kind": "Gdef"}, "HAS_SSLv3": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_SSLv3", "kind": "Gdef"}, "HAS_TLSv1": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_TLSv1", "kind": "Gdef"}, "HAS_TLSv1_1": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_TLSv1_1", "kind": "Gdef"}, "HAS_TLSv1_2": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_TLSv1_2", "kind": "Gdef"}, "HAS_TLSv1_3": {".class": "SymbolTableNode", "cross_ref": "_ssl.HAS_TLSv1_3", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MemoryBIO": {".class": "SymbolTableNode", "cross_ref": "_ssl.MemoryBIO", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Never": {".class": "SymbolTableNode", "cross_ref": "typing.Never", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OPENSSL_VERSION": {".class": "SymbolTableNode", "cross_ref": "_ssl.OPENSSL_VERSION", "kind": "Gdef"}, "OPENSSL_VERSION_INFO": {".class": "SymbolTableNode", "cross_ref": "_ssl.OPENSSL_VERSION_INFO", "kind": "Gdef"}, "OPENSSL_VERSION_NUMBER": {".class": "SymbolTableNode", "cross_ref": "_ssl.OPENSSL_VERSION_NUMBER", "kind": "Gdef"}, "OP_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_ALL", "name": "OP_ALL", "type": "ssl.Options"}}, "OP_CIPHER_SERVER_PREFERENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_CIPHER_SERVER_PREFERENCE", "name": "OP_CIPHER_SERVER_PREFERENCE", "type": "ssl.Options"}}, "OP_ENABLE_KTLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_ENABLE_KTLS", "name": "OP_ENABLE_KTLS", "type": "ssl.Options"}}, "OP_ENABLE_MIDDLEBOX_COMPAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_ENABLE_MIDDLEBOX_COMPAT", "name": "OP_ENABLE_MIDDLEBOX_COMPAT", "type": "ssl.Options"}}, "OP_IGNORE_UNEXPECTED_EOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_IGNORE_UNEXPECTED_EOF", "name": "OP_IGNORE_UNEXPECTED_EOF", "type": "ssl.Options"}}, "OP_LEGACY_SERVER_CONNECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_LEGACY_SERVER_CONNECT", "name": "OP_LEGACY_SERVER_CONNECT", "type": "ssl.Options"}}, "OP_NO_COMPRESSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_COMPRESSION", "name": "OP_NO_COMPRESSION", "type": "ssl.Options"}}, "OP_NO_RENEGOTIATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_RENEGOTIATION", "name": "OP_NO_RENEGOTIATION", "type": "ssl.Options"}}, "OP_NO_SSLv2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_SSLv2", "name": "OP_NO_SSLv2", "type": "ssl.Options"}}, "OP_NO_SSLv3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_SSLv3", "name": "OP_NO_SSLv3", "type": "ssl.Options"}}, "OP_NO_TICKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_TICKET", "name": "OP_NO_TICKET", "type": "ssl.Options"}}, "OP_NO_TLSv1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_TLSv1", "name": "OP_NO_TLSv1", "type": "ssl.Options"}}, "OP_NO_TLSv1_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_TLSv1_1", "name": "OP_NO_TLSv1_1", "type": "ssl.Options"}}, "OP_NO_TLSv1_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_TLSv1_2", "name": "OP_NO_TLSv1_2", "type": "ssl.Options"}}, "OP_NO_TLSv1_3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_NO_TLSv1_3", "name": "OP_NO_TLSv1_3", "type": "ssl.Options"}}, "OP_SINGLE_DH_USE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_SINGLE_DH_USE", "name": "OP_SINGLE_DH_USE", "type": "ssl.Options"}}, "OP_SINGLE_ECDH_USE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.OP_SINGLE_ECDH_USE", "name": "OP_SINGLE_ECDH_USE", "type": "ssl.Options"}}, "Options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.Options", "name": "Options", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl.Options", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl.Options", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "OP_ALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_ALL", "name": "OP_ALL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2147483728}, "type_ref": "builtins.int"}}}, "OP_CIPHER_SERVER_PREFERENCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_CIPHER_SERVER_PREFERENCE", "name": "OP_CIPHER_SERVER_PREFERENCE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4194304}, "type_ref": "builtins.int"}}}, "OP_ENABLE_KTLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_ENABLE_KTLS", "name": "OP_ENABLE_KTLS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "OP_ENABLE_MIDDLEBOX_COMPAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_ENABLE_MIDDLEBOX_COMPAT", "name": "OP_ENABLE_MIDDLEBOX_COMPAT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1048576}, "type_ref": "builtins.int"}}}, "OP_IGNORE_UNEXPECTED_EOF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_IGNORE_UNEXPECTED_EOF", "name": "OP_IGNORE_UNEXPECTED_EOF", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "OP_LEGACY_SERVER_CONNECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_LEGACY_SERVER_CONNECT", "name": "OP_LEGACY_SERVER_CONNECT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "OP_NO_COMPRESSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_COMPRESSION", "name": "OP_NO_COMPRESSION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 131072}, "type_ref": "builtins.int"}}}, "OP_NO_RENEGOTIATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_RENEGOTIATION", "name": "OP_NO_RENEGOTIATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1073741824}, "type_ref": "builtins.int"}}}, "OP_NO_SSLv2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_SSLv2", "name": "OP_NO_SSLv2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "OP_NO_SSLv3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_SSLv3", "name": "OP_NO_SSLv3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 33554432}, "type_ref": "builtins.int"}}}, "OP_NO_TICKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_TICKET", "name": "OP_NO_TICKET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16384}, "type_ref": "builtins.int"}}}, "OP_NO_TLSv1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_TLSv1", "name": "OP_NO_TLSv1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 67108864}, "type_ref": "builtins.int"}}}, "OP_NO_TLSv1_1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_TLSv1_1", "name": "OP_NO_TLSv1_1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 268435456}, "type_ref": "builtins.int"}}}, "OP_NO_TLSv1_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_TLSv1_2", "name": "OP_NO_TLSv1_2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 134217728}, "type_ref": "builtins.int"}}}, "OP_NO_TLSv1_3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_NO_TLSv1_3", "name": "OP_NO_TLSv1_3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 536870912}, "type_ref": "builtins.int"}}}, "OP_SINGLE_DH_USE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_SINGLE_DH_USE", "name": "OP_SINGLE_DH_USE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "OP_SINGLE_ECDH_USE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.Options.OP_SINGLE_ECDH_USE", "name": "OP_SINGLE_ECDH_USE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.Options.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.Options", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PEM_FOOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PEM_FOOTER", "name": "PEM_FOOTER", "type": "builtins.str"}}, "PEM_HEADER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PEM_HEADER", "name": "PEM_HEADER", "type": "builtins.str"}}, "PEM_cert_to_DER_cert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pem_cert_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.PEM_cert_to_DER_cert", "name": "PEM_cert_to_DER_cert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pem_cert_string"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "PEM_cert_to_DER_cert", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PROTOCOL_SSLv2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_SSLv2", "name": "PROTOCOL_SSLv2", "type": "ssl._SSLMethod"}}, "PROTOCOL_SSLv23": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_SSLv23", "name": "PROTOCOL_SSLv23", "type": "ssl._SSLMethod"}}, "PROTOCOL_SSLv3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_SSLv3", "name": "PROTOCOL_SSLv3", "type": "ssl._SSLMethod"}}, "PROTOCOL_TLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_TLS", "name": "PROTOCOL_TLS", "type": "ssl._SSLMethod"}}, "PROTOCOL_TLS_CLIENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_TLS_CLIENT", "name": "PROTOCOL_TLS_CLIENT", "type": "ssl._SSLMethod"}}, "PROTOCOL_TLS_SERVER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_TLS_SERVER", "name": "PROTOCOL_TLS_SERVER", "type": "ssl._SSLMethod"}}, "PROTOCOL_TLSv1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_TLSv1", "name": "PROTOCOL_TLSv1", "type": "ssl._SSLMethod"}}, "PROTOCOL_TLSv1_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_TLSv1_1", "name": "PROTOCOL_TLSv1_1", "type": "ssl._SSLMethod"}}, "PROTOCOL_TLSv1_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.PROTOCOL_TLSv1_2", "name": "PROTOCOL_TLSv1_2", "type": "ssl._SSLMethod"}}, "Purpose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl._ASN1Object", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.<PERSON><PERSON><PERSON>", "name": "Purpose", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl.<PERSON><PERSON><PERSON>", "ssl._ASN1Object", "ssl._ASN1ObjectBase", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CLIENT_AUTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "ssl.Purpose.CLIENT_AUTH", "name": "CLIENT_AUTH", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 130}, "type_ref": "builtins.int"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "clientAuth"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TLS Web Client Authentication"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "1.3.6.1.5.5.7.3.1"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "SERVER_AUTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "ssl.Purpose.SERVER_AUTH", "name": "SERVER_AUTH", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 129}, "type_ref": "builtins.int"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "serverAuth"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TLS Web Server Authentication"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "1.3.6.1.5.5.7.3.2"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "ssl.Purpose.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.Purpose.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl.<PERSON><PERSON><PERSON>"}, "values": [], "variance": 0}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Purpose", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.Purpose.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl.<PERSON><PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.Purpose.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl.<PERSON><PERSON><PERSON>"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.Purpose.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl.<PERSON><PERSON><PERSON>"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "type_vars": [], "typeddict_type": null}}, "RAND_add": {".class": "SymbolTableNode", "cross_ref": "_ssl.RAND_add", "kind": "Gdef"}, "RAND_bytes": {".class": "SymbolTableNode", "cross_ref": "_ssl.RAND_bytes", "kind": "Gdef"}, "RAND_status": {".class": "SymbolTableNode", "cross_ref": "_ssl.RAND_status", "kind": "Gdef"}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SOCK_STREAM", "name": "SOCK_STREAM", "type": "builtins.int"}}, "SOL_SOCKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SOL_SOCKET", "name": "SOL_SOCKET", "type": "builtins.int"}}, "SO_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SO_TYPE", "name": "SO_TYPE", "type": "builtins.int"}}, "SSLCertVerificationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl.SSLError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLCertVerificationError", "name": "SSLCertVerificationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLCertVerificationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLCertVerificationError", "ssl.SSLError", "builtins.OSError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "verify_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLCertVerificationError.verify_code", "name": "verify_code", "type": "builtins.int"}}, "verify_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLCertVerificationError.verify_message", "name": "verify_message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLCertVerificationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLCertVerificationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ssl._SSLContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLContext", "name": "SSLContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLContext", "_ssl._SSLContext", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["cls", "protocol", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "ssl.SSLContext.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["cls", "protocol", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLContext", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of SSLContext", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLContext", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLContext", "values": [], "variance": 0}]}}}, "get_ca_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.get_ca_certs", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of SSLContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.get_ciphers", "name": "get_ciphers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ciphers of SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._Cipher"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hostname_checks_common_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.hostname_checks_common_name", "name": "hostname_checks_common_name", "type": "builtins.bool"}}, "keylog_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.keylog_filename", "name": "keylog_filename", "type": "builtins.str"}}, "load_default_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "purpose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.load_default_certs", "name": "load_default_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "purpose"], "arg_types": ["ssl.SSLContext", {".class": "TypeAliasType", "args": [], "type_ref": "ssl.<PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_default_certs of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_dh_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.load_dh_params", "name": "load_dh_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ssl.SSLContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_dh_params of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_verify_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "cafile", "<PERSON><PERSON>", "cadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.load_verify_locations", "name": "load_verify_locations", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "cafile", "<PERSON><PERSON>", "cadata"], "arg_types": ["ssl.SSLContext", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_verify_locations of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maximum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.maximum_version", "name": "maximum_version", "type": "ssl.TLSVersion"}}, "minimum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.minimum_version", "name": "minimum_version", "type": "ssl.TLSVersion"}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.options", "name": "options", "type": "ssl.Options"}}, "post_handshake_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.post_handshake_auth", "name": "post_handshake_auth", "type": "builtins.bool"}}, "protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ssl.SSLContext.protocol", "name": "protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "protocol of SSLContext", "ret_type": "ssl._SSLMethod", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ssl.SSLContext.protocol", "name": "protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "protocol of SSLContext", "ret_type": "ssl._SSLMethod", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "security_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.security_level", "name": "security_level", "type": "builtins.int"}}, "set_alpn_protocols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alpn_protocols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.set_alpn_protocols", "name": "set_alpn_protocols", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alpn_protocols"], "arg_types": ["ssl.SSLContext", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_alpn_protocols of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.set_ciphers", "name": "set_ciphers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ssl.SSLContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ciphers of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_default_verify_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.set_default_verify_paths", "name": "set_default_verify_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_verify_paths of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ecdh_curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.set_ecdh_curve", "name": "set_ecdh_curve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ssl.SSLContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ecdh_curve of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_npn_protocols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "npn_protocols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.set_npn_protocols", "name": "set_npn_protocols", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "npn_protocols"], "arg_types": ["ssl.SSLContext", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_npn_protocols of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_servername_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "server_name_callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.set_servername_callback", "name": "set_servername_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "server_name_callback"], "arg_types": ["ssl.SSLContext", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._SrvnmeCbType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_servername_callback of SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sslobject_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.sslobject_class", "name": "sslobject_class", "type": {".class": "TypeType", "item": "ssl.SSLObject"}}}, "sslsocket_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.sslsocket_class", "name": "sslsocket_class", "type": {".class": "TypeType", "item": "ssl.SSLSocket"}}}, "verify_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.verify_flags", "name": "verify_flags", "type": "ssl.VerifyFlags"}}, "verify_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLContext.verify_mode", "name": "verify_mode", "type": "ssl.VerifyMode"}}, "wrap_bio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "incoming", "outgoing", "server_side", "server_hostname", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.wrap_bio", "name": "wrap_bio", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "incoming", "outgoing", "server_side", "server_hostname", "session"], "arg_types": ["ssl.SSLContext", "_ssl.MemoryBIO", "_ssl.MemoryBIO", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_ssl.SSLSession", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_bio of SSLContext", "ret_type": "ssl.SSLObject", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wrap_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "sock", "server_side", "do_handshake_on_connect", "suppress_ragged_eofs", "server_hostname", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLContext.wrap_socket", "name": "wrap_socket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "sock", "server_side", "do_handshake_on_connect", "suppress_ragged_eofs", "server_hostname", "session"], "arg_types": ["ssl.SSLContext", "socket.socket", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_ssl.SSLSession", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_socket of SSLContext", "ret_type": "ssl.SSLSocket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLEOFError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl.SSLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLEOFError", "name": "SSLEOFError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLEOFError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLEOFError", "ssl.SSLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLEOFError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLEOFError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLError", "name": "SSLError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "library": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLError.library", "name": "library", "type": "builtins.str"}}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLError.reason", "name": "reason", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLErrorNumber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLErrorNumber", "name": "SSLErrorNumber", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl.SSLErrorNumber", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLErrorNumber", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "SSL_ERROR_EOF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_EOF", "name": "SSL_ERROR_EOF", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "SSL_ERROR_INVALID_ERROR_CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_INVALID_ERROR_CODE", "name": "SSL_ERROR_INVALID_ERROR_CODE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "SSL_ERROR_SSL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_SSL", "name": "SSL_ERROR_SSL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "SSL_ERROR_SYSCALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_SYSCALL", "name": "SSL_ERROR_SYSCALL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "SSL_ERROR_WANT_CONNECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_WANT_CONNECT", "name": "SSL_ERROR_WANT_CONNECT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, "type_ref": "builtins.int"}}}, "SSL_ERROR_WANT_READ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_WANT_READ", "name": "SSL_ERROR_WANT_READ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "SSL_ERROR_WANT_WRITE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_WANT_WRITE", "name": "SSL_ERROR_WANT_WRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "SSL_ERROR_WANT_X509_LOOKUP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_WANT_X509_LOOKUP", "name": "SSL_ERROR_WANT_X509_LOOKUP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "SSL_ERROR_ZERO_RETURN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.SSLErrorNumber.SSL_ERROR_ZERO_RETURN", "name": "SSL_ERROR_ZERO_RETURN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLErrorNumber.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLErrorNumber", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLObject", "name": "SSLObject", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLObject", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLObject", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["ssl.SSLObject", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSLObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cipher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.cipher", "name": "cipher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cipher of SSLObject", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.compression", "name": "compression", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compression of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLObject.context", "name": "context", "type": "ssl.SSLContext"}}, "do_handshake": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.do_handshake", "name": "do_handshake", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_handshake of SSLObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_channel_binding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "cb_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.get_channel_binding", "name": "get_channel_binding", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "cb_type"], "arg_types": ["ssl.SSLObject", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_channel_binding of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getpeercert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.getpeercert", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLObject.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLObject.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLObject.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLObject.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLObject.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLObject.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLObject", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLObject", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "pending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.pending", "name": "pending", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pending of SSLObject", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "len", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "len", "buffer"], "arg_types": ["ssl.SSLObject", "builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of SSLObject", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selected_alpn_protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.selected_alpn_protocol", "name": "selected_alpn_protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selected_alpn_protocol of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selected_npn_protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.selected_npn_protocol", "name": "selected_npn_protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selected_npn_protocol of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ssl.SSLObject.server_hostname", "name": "server_hostname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "server_hostname of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ssl.SSLObject.server_hostname", "name": "server_hostname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "server_hostname of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "server_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ssl.SSLObject.server_side", "name": "server_side", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "server_side of SSLObject", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ssl.SSLObject.server_side", "name": "server_side", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "server_side of SSLObject", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLObject.session", "name": "session", "type": {".class": "UnionType", "items": ["_ssl.SSLSession", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "session_reused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ssl.SSLObject.session_reused", "name": "session_reused", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session_reused of SSLObject", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ssl.SSLObject.session_reused", "name": "session_reused", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session_reused of SSLObject", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shared_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.shared_ciphers", "name": "shared_ciphers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shared_ciphers of SSLObject", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unwrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.unwrap", "name": "unwrap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap of SSLObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_client_post_handshake": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.verify_client_post_handshake", "name": "verify_client_post_handshake", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_client_post_handshake of SSLObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLObject"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of SSLObject", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLObject.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["ssl.SSLObject", "_collections_abc.<PERSON><PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of SSLObject", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLObject.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLObject", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLSession": {".class": "SymbolTableNode", "cross_ref": "_ssl.SSLSession", "kind": "Gdef"}, "SSLSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["socket.socket"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLSocket", "name": "SSLSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLSocket", "socket.socket", "_socket.socket", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["ssl.SSLSocket", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSLSocket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.accept", "name": "accept", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept of SSLSocket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["ssl.SSLSocket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cipher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.cipher", "name": "cipher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cipher of SSLSocket", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.compression", "name": "compression", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compression of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "arg_types": ["ssl.SSLSocket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of SSLSocket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect_ex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.connect_ex", "name": "connect_ex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "arg_types": ["ssl.SSLSocket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect_ex of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLSocket.context", "name": "context", "type": "ssl.SSLContext"}}, "do_handshake": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.do_handshake", "name": "do_handshake", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "block"], "arg_types": ["ssl.SSLSocket", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_handshake of SSLSocket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_channel_binding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "cb_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.get_channel_binding", "name": "get_channel_binding", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "cb_type"], "arg_types": ["ssl.SSLSocket", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_channel_binding of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getpeercert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.getpeercert", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLSocket.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLSocket.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLSocket.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLSocket.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLSocket.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLSocket.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["ssl.SSLSocket", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpeercert of SSLSocket", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "pending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.pending", "name": "pending", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pending of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "len", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "len", "buffer"], "arg_types": ["ssl.SSLSocket", "builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of SSLSocket", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "buflen", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.recv", "name": "recv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "buflen", "flags"], "arg_types": ["ssl.SSLSocket", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recv of SSLSocket", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.recv_into", "name": "recv_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recv_into of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvfrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "buflen", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.recvfrom", "name": "recvfrom", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "buflen", "flags"], "arg_types": ["ssl.SSLSocket", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recvfrom of SSLSocket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvfrom_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.recvfrom_into", "name": "recvfrom_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recvfrom_into of SSLSocket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvmsg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.recvmsg", "name": "recvmsg", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["ssl.SSLSocket", {".class": "UninhabitedType"}, {".class": "UninhabitedType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recvmsg of SSLSocket", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recvmsg_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.recvmsg_into", "name": "recvmsg_into", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["ssl.SSLSocket", {".class": "UninhabitedType"}, {".class": "UninhabitedType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recvmsg_into of SSLSocket", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selected_alpn_protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.selected_alpn_protocol", "name": "selected_alpn_protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selected_alpn_protocol of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selected_npn_protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.selected_npn_protocol", "name": "selected_npn_protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selected_npn_protocol of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.sendall", "name": "sendall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendall of SSLSocket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendmsg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.sendmsg", "name": "sendmsg", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["ssl.SSLSocket", {".class": "UninhabitedType"}, {".class": "UninhabitedType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendmsg of SSLSocket", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.sendto", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "flags_or_addr", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLSocket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "flags_or_addr", "addr"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendto of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLSocket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "flags_or_addr", "addr"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendto of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "flags_or_addr", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ssl.SSLSocket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "flags_or_addr", "addr"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendto of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ssl.SSLSocket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "flags_or_addr", "addr"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendto of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "flags_or_addr", "addr"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendto of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "flags_or_addr", "addr"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendto of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "server_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLSocket.server_hostname", "name": "server_hostname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLSocket.server_side", "name": "server_side", "type": "builtins.bool"}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl.SSLSocket.session", "name": "session", "type": {".class": "UnionType", "items": ["_ssl.SSLSession", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "session_reused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ssl.SSLSocket.session_reused", "name": "session_reused", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session_reused of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ssl.SSLSocket.session_reused", "name": "session_reused", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session_reused of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shared_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.shared_ciphers", "name": "shared_ciphers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shared_ciphers of SSLSocket", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "how"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "how"], "arg_types": ["ssl.SSLSocket", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown of SSLSocket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unwrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.unwrap", "name": "unwrap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap of SSLSocket", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_client_post_handshake": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.verify_client_post_handshake", "name": "verify_client_post_handshake", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_client_post_handshake of SSLSocket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ssl.SSLSocket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of SSLSocket", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.SSLSocket.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["ssl.SSLSocket", "_collections_abc.<PERSON><PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of SSLSocket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLSyscallError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl.SSLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLSyscallError", "name": "SSLSyscallError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLSyscallError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLSyscallError", "ssl.SSLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLSyscallError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLSyscallError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLWantReadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl.SSLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLWantReadError", "name": "SSLWantReadError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLWantReadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLWantReadError", "ssl.SSLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLWantReadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLWantReadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLWantWriteError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl.SSLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLWantWriteError", "name": "SSLWantWriteError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLWantWriteError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLWantWriteError", "ssl.SSLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLWantWriteError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLWantWriteError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLZeroReturnError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl.SSLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.SSLZeroReturnError", "name": "SSLZeroReturnError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl.SSLZeroReturnError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl.SSLZeroReturnError", "ssl.SSLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.SSLZeroReturnError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.SSLZeroReturnError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSL_ERROR_EOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_EOF", "name": "SSL_ERROR_EOF", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_INVALID_ERROR_CODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_INVALID_ERROR_CODE", "name": "SSL_ERROR_INVALID_ERROR_CODE", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_SSL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_SSL", "name": "SSL_ERROR_SSL", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_SYSCALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_SYSCALL", "name": "SSL_ERROR_SYSCALL", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_WANT_CONNECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_WANT_CONNECT", "name": "SSL_ERROR_WANT_CONNECT", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_WANT_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_WANT_READ", "name": "SSL_ERROR_WANT_READ", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_WANT_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_WANT_WRITE", "name": "SSL_ERROR_WANT_WRITE", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_WANT_X509_LOOKUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_WANT_X509_LOOKUP", "name": "SSL_ERROR_WANT_X509_LOOKUP", "type": "ssl.SSLErrorNumber"}}, "SSL_ERROR_ZERO_RETURN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.SSL_ERROR_ZERO_RETURN", "name": "SSL_ERROR_ZERO_RETURN", "type": "ssl.SSLErrorNumber"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TLSVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.TLSVersion", "name": "TLSVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl.TLSVersion", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl.TLSVersion", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "MAXIMUM_SUPPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.TLSVersion.MAXIMUM_SUPPORTED", "name": "MAXIMUM_SUPPORTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "type_ref": "builtins.int"}}}, "MINIMUM_SUPPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.TLSVersion.MINIMUM_SUPPORTED", "name": "MINIMUM_SUPPORTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": -2}, "type_ref": "builtins.int"}}}, "SSLv3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.TLSVersion.SSLv3", "name": "SSLv3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 768}, "type_ref": "builtins.int"}}}, "TLSv1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.TLSVersion.TLSv1", "name": "TLSv1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 769}, "type_ref": "builtins.int"}}}, "TLSv1_1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.TLSVersion.TLSv1_1", "name": "TLSv1_1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 770}, "type_ref": "builtins.int"}}}, "TLSv1_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.TLSVersion.TLSv1_2", "name": "TLSv1_2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 771}, "type_ref": "builtins.int"}}}, "TLSv1_3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.TLSVersion.TLSv1_3", "name": "TLSv1_3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 772}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.TLSVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.TLSVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VERIFY_ALLOW_PROXY_CERTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.VERIFY_ALLOW_PROXY_CERTS", "name": "VERIFY_ALLOW_PROXY_CERTS", "type": "ssl.VerifyFlags"}}, "VERIFY_CRL_CHECK_CHAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.VERIFY_CRL_CHECK_CHAIN", "name": "VERIFY_CRL_CHECK_CHAIN", "type": "ssl.VerifyFlags"}}, "VERIFY_CRL_CHECK_LEAF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.VERIFY_CRL_CHECK_LEAF", "name": "VERIFY_CRL_CHECK_LEAF", "type": "ssl.VerifyFlags"}}, "VERIFY_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.VERIFY_DEFAULT", "name": "VERIFY_DEFAULT", "type": "ssl.VerifyFlags"}}, "VERIFY_X509_PARTIAL_CHAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.VERIFY_X509_PARTIAL_CHAIN", "name": "VERIFY_X509_PARTIAL_CHAIN", "type": "ssl.VerifyFlags"}}, "VERIFY_X509_STRICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.VERIFY_X509_STRICT", "name": "VERIFY_X509_STRICT", "type": "ssl.VerifyFlags"}}, "VERIFY_X509_TRUSTED_FIRST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.VERIFY_X509_TRUSTED_FIRST", "name": "VERIFY_X509_TRUSTED_FIRST", "type": "ssl.VerifyFlags"}}, "VerifyFlags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.VerifyFlags", "name": "VerifyFlags", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl.VerifyFlags", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl.VerifyFlags", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "VERIFY_ALLOW_PROXY_CERTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyFlags.VERIFY_ALLOW_PROXY_CERTS", "name": "VERIFY_ALLOW_PROXY_CERTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "VERIFY_CRL_CHECK_CHAIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyFlags.VERIFY_CRL_CHECK_CHAIN", "name": "VERIFY_CRL_CHECK_CHAIN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 12}, "type_ref": "builtins.int"}}}, "VERIFY_CRL_CHECK_LEAF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyFlags.VERIFY_CRL_CHECK_LEAF", "name": "VERIFY_CRL_CHECK_LEAF", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "VERIFY_DEFAULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyFlags.VERIFY_DEFAULT", "name": "VERIFY_DEFAULT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "VERIFY_X509_PARTIAL_CHAIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyFlags.VERIFY_X509_PARTIAL_CHAIN", "name": "VERIFY_X509_PARTIAL_CHAIN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 524288}, "type_ref": "builtins.int"}}}, "VERIFY_X509_STRICT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyFlags.VERIFY_X509_STRICT", "name": "VERIFY_X509_STRICT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "VERIFY_X509_TRUSTED_FIRST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyFlags.VERIFY_X509_TRUSTED_FIRST", "name": "VERIFY_X509_TRUSTED_FIRST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32768}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.VerifyFlags.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.VerifyFlags", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VerifyMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl.VerifyMode", "name": "VerifyMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl.VerifyMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl.VerifyMode", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CERT_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyMode.CERT_NONE", "name": "CERT_NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "CERT_OPTIONAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyMode.CERT_OPTIONAL", "name": "CERT_OPTIONAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "CERT_REQUIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl.VerifyMode.CERT_REQUIRED", "name": "CERT_REQUIRED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl.VerifyMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl.VerifyMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ASN1Object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ssl._ASN1ObjectBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl._ASN1Object", "name": "_ASN1Object", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl._ASN1Object", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "ssl", "mro": ["ssl._ASN1Object", "ssl._ASN1ObjectBase", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "oid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "ssl._ASN1Object.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "oid"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _ASN1Object", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}]}}}, "fromname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ssl._ASN1Object.fromname", "name": "fromname", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromname of _ASN1Object", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "ssl._ASN1Object.fromname", "name": "fromname", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromname of _ASN1Object", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}]}}}}, "fromnid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "nid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ssl._ASN1Object.fromnid", "name": "fromnid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "nid"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromnid of _ASN1Object", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "ssl._ASN1Object.fromnid", "name": "fromnid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "nid"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromnid of _ASN1Object", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1Object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1Object"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1ObjectBase"}, "type_vars": [], "typeddict_type": null}}, "_ASN1ObjectBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl._ASN1ObjectBase", "name": "_ASN1ObjectBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "ssl._ASN1ObjectBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["nid", "shortname", "longname", "oid"]}}, "module_name": "ssl", "mro": ["ssl._ASN1ObjectBase", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl._ASN1ObjectBase.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl._ASN1ObjectBase.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl._ASN1ObjectBase.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "nid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shortname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "longname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "oid"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "nid", "shortname", "longname", "oid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "ssl._ASN1ObjectBase.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "nid", "shortname", "longname", "oid"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _ASN1ObjectBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl._ASN1ObjectBase._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _ASN1ObjectBase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl._ASN1ObjectBase._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl._ASN1ObjectBase._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl._ASN1ObjectBase._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ssl._ASN1ObjectBase._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _ASN1ObjectBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "ssl._ASN1ObjectBase._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _ASN1ObjectBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "nid", "shortname", "longname", "oid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl._ASN1ObjectBase._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "nid", "shortname", "longname", "oid"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _ASN1ObjectBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase._NT", "id": -1, "name": "_NT", "namespace": "ssl._ASN1ObjectBase._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ssl._ASN1ObjectBase._source", "name": "_source", "type": "builtins.str"}}, "longname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl._ASN1ObjectBase.longname", "name": "longname", "type": "builtins.str"}}, "longname-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl._ASN1ObjectBase.longname", "kind": "<PERSON><PERSON><PERSON>"}, "nid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl._ASN1ObjectBase.nid", "name": "nid", "type": "builtins.int"}}, "nid-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl._ASN1ObjectBase.nid", "kind": "<PERSON><PERSON><PERSON>"}, "oid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl._ASN1ObjectBase.oid", "name": "oid", "type": "builtins.str"}}, "oid-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl._ASN1ObjectBase.oid", "kind": "<PERSON><PERSON><PERSON>"}, "shortname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ssl._ASN1ObjectBase.shortname", "name": "shortname", "type": "builtins.str"}}, "shortname-redefinition": {".class": "SymbolTableNode", "cross_ref": "ssl._ASN1ObjectBase.shortname", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._ASN1ObjectBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": "ssl._ASN1ObjectBase"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_Cipher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl._Cipher", "name": "_Cipher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ssl._Cipher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ssl", "mro": ["ssl._Cipher", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["aead", "builtins.bool"], ["alg_bits", "builtins.int"], ["auth", "builtins.str"], ["description", "builtins.str"], ["digest", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["id", "builtins.int"], ["kea", "builtins.str"], ["name", "builtins.str"], ["protocol", "builtins.str"], ["strength_bits", "builtins.int"], ["symmetric", "builtins.str"]], "readonly_keys": [], "required_keys": ["aead", "alg_bits", "auth", "description", "digest", "id", "kea", "name", "protocol", "strength_bits", "symmetric"]}}}, "_DEFAULT_CIPHERS": {".class": "SymbolTableNode", "cross_ref": "_ssl._DEFAULT_CIPHERS", "kind": "Gdef"}, "_OPENSSL_API_VERSION": {".class": "SymbolTableNode", "cross_ref": "_ssl._OPENSSL_API_VERSION", "kind": "Gdef"}, "_PCTRTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ssl._PCTRTT", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_PCTRTTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ssl._PCTRTTT", "line": 46, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PCTRTT"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_PasswordType": {".class": "SymbolTableNode", "cross_ref": "_ssl._PasswordType", "kind": "Gdef"}, "_PeerCertRetDictType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ssl._PeerCertRetDictType", "line": 47, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PCTRTTT"}, {".class": "TypeAliasType", "args": [], "type_ref": "ssl._PCTRTT"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_PeerCertRetType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ssl._PeerCertRetType", "line": 48, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl._PeerCertRetDictType"}, "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_SSLContext": {".class": "SymbolTableNode", "cross_ref": "_ssl._SSLContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SSLMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ssl._SSLMethod", "name": "_SSLMethod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "ssl._SSLMethod", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "ssl", "mro": ["ssl._SSLMethod", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PROTOCOL_SSLv2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_SSLv2", "name": "PROTOCOL_SSLv2", "type": "types.EllipsisType"}}, "PROTOCOL_SSLv23": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_SSLv23", "name": "PROTOCOL_SSLv23", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "PROTOCOL_SSLv3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_SSLv3", "name": "PROTOCOL_SSLv3", "type": "types.EllipsisType"}}, "PROTOCOL_TLS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_TLS", "name": "PROTOCOL_TLS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "PROTOCOL_TLS_CLIENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_TLS_CLIENT", "name": "PROTOCOL_TLS_CLIENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "PROTOCOL_TLS_SERVER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_TLS_SERVER", "name": "PROTOCOL_TLS_SERVER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 17}, "type_ref": "builtins.int"}}}, "PROTOCOL_TLSv1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_TLSv1", "name": "PROTOCOL_TLSv1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "PROTOCOL_TLSv1_1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_TLSv1_1", "name": "PROTOCOL_TLSv1_1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "PROTOCOL_TLSv1_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ssl._SSLMethod.PROTOCOL_TLSv1_2", "name": "PROTOCOL_TLSv1_2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ssl._SSLMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ssl._SSLMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SrvnmeCbType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ssl._SrvnmeCbType", "line": 49, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "UnionType", "items": ["ssl.SSLSocket", "ssl.SSLObject"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "ssl.SSLSocket"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_create_default_https_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ssl._create_default_https_context", "name": "_create_default_https_context", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "ssl.SSLContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_unverified_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["protocol", "cert_reqs", "check_hostname", "purpose", "certfile", "keyfile", "cafile", "<PERSON><PERSON>", "cadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl._create_unverified_context", "name": "_create_unverified_context", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["protocol", "cert_reqs", "check_hostname", "purpose", "certfile", "keyfile", "cafile", "<PERSON><PERSON>", "cadata"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "ssl.<PERSON><PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_unverified_context", "ret_type": "ssl.SSLContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cert_time_to_seconds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cert_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.cert_time_to_seconds", "name": "cert_time_to_seconds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cert_time"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cert_time_to_seconds", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_default_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5], "arg_names": ["purpose", "cafile", "<PERSON><PERSON>", "cadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.create_default_context", "name": "create_default_context", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5], "arg_names": ["purpose", "cafile", "<PERSON><PERSON>", "cadata"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "ssl.<PERSON><PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_default_context", "ret_type": "ssl.SSLContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "enum_certificates": {".class": "SymbolTableNode", "cross_ref": "_ssl.enum_certificates", "kind": "Gdef"}, "enum_crls": {".class": "SymbolTableNode", "cross_ref": "_ssl.enum_crls", "kind": "Gdef"}, "get_default_verify_paths": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.get_default_verify_paths", "name": "get_default_verify_paths", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_verify_paths", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "ssl.DefaultVerifyPaths"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_protocol_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["protocol_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.get_protocol_name", "name": "get_protocol_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["protocol_code"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_protocol_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_server_certificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["addr", "ssl_version", "ca_certs", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ssl.get_server_certificate", "name": "get_server_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["addr", "ssl_version", "ca_certs", "timeout"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_server_certificate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "socket_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ssl.socket_error", "line": 51, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.OSError"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\ssl.pyi"}