{".class": "MypyFile", "_fullname": "types", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsyncGenerator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncGenerator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsyncGeneratorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.AsyncGeneratorType", "name": "AsyncGeneratorType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.AsyncGeneratorType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "types", "mro": ["types.AsyncGeneratorType", "typing.AsyncGenerator", "typing.AsyncIterator", "typing.AsyncIterable", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.AsyncGeneratorType.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.AsyncGeneratorType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.AsyncGeneratorType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, "values": [], "variance": 0}]}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.AsyncGeneratorType.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__anext__ of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "types.AsyncGeneratorType.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__class_getitem__ of AsyncGeneratorType", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.AsyncGeneratorType.__name__", "name": "__name__", "type": "builtins.str"}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.AsyncGeneratorType.__qualname__", "name": "__qualname__", "type": "builtins.str"}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.AsyncGeneratorType.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ag_await": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.AsyncGeneratorType.ag_await", "name": "ag_await", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_await of AsyncGeneratorType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.AsyncGeneratorType.ag_await", "name": "ag_await", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_await of AsyncGeneratorType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ag_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.AsyncGeneratorType.ag_code", "name": "ag_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_code of AsyncGeneratorType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.AsyncGeneratorType.ag_code", "name": "ag_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_code of AsyncGeneratorType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ag_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.AsyncGeneratorType.ag_frame", "name": "ag_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_frame of AsyncGeneratorType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.AsyncGeneratorType.ag_frame", "name": "ag_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_frame of AsyncGeneratorType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ag_running": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.AsyncGeneratorType.ag_running", "name": "ag_running", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_running of AsyncGeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.AsyncGeneratorType.ag_running", "name": "ag_running", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_running of AsyncGeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ag_suspended": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.AsyncGeneratorType.ag_suspended", "name": "ag_suspended", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_suspended of AsyncGeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.AsyncGeneratorType.ag_suspended", "name": "ag_suspended", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ag_suspended of AsyncGeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "asend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.AsyncGeneratorType.asend", "name": "asend", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asend of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "athrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "types.AsyncGeneratorType.athrow", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "types.AsyncGeneratorType.athrow", "name": "athrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "athrow of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.AsyncGeneratorType.athrow", "name": "athrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "athrow of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "types.AsyncGeneratorType.athrow", "name": "athrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "athrow of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.AsyncGeneratorType.athrow", "name": "athrow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "athrow of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "athrow of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "athrow of AsyncGeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.AsyncGeneratorType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.AsyncGeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "types.AsyncGeneratorType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_YieldT_co", "_SendT_contra"], "typeddict_type": null}}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BuiltinFunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.BuiltinFunctionType", "name": "BuiltinFunctionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.BuiltinFunctionType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.BuiltinFunctionType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.BuiltinFunctionType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["types.BuiltinFunctionType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of BuiltinFunctionType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.BuiltinFunctionType.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.BuiltinFunctionType", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of BuiltinFunctionType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.BuiltinFunctionType.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of BuiltinFunctionType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.BuiltinFunctionType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of BuiltinFunctionType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.BuiltinFunctionType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of BuiltinFunctionType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.BuiltinFunctionType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of BuiltinFunctionType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.BuiltinFunctionType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of BuiltinFunctionType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__self__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.BuiltinFunctionType.__self__", "name": "__self__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__self__ of BuiltinFunctionType", "ret_type": {".class": "UnionType", "items": ["builtins.object", "types.ModuleType"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.BuiltinFunctionType.__self__", "name": "__self__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.BuiltinFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__self__ of BuiltinFunctionType", "ret_type": {".class": "UnionType", "items": ["builtins.object", "types.ModuleType"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BuiltinMethodType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "types.BuiltinMethodType", "line": 494, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "types.BuiltinFunctionType"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CellType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.CellType", "name": "CellType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.CellType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.CellType", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "types.CellType.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "types.CellType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CellType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CellType", "values": [], "variance": 0}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of CellType", "ret_type": "types.CellType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CellType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CellType", "values": [], "variance": 0}]}}}, "cell_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.CellType.cell_contents", "name": "cell_contents", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CellType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CellType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassMethodDescriptorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.ClassMethodDescriptorType", "name": "ClassMethodDescriptorType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.ClassMethodDescriptorType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.ClassMethodDescriptorType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.ClassMethodDescriptorType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["types.ClassMethodDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ClassMethodDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.ClassMethodDescriptorType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.ClassMethodDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ClassMethodDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.ClassMethodDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ClassMethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of ClassMethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.ClassMethodDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ClassMethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of ClassMethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__objclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.ClassMethodDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ClassMethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of ClassMethodDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.ClassMethodDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ClassMethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of ClassMethodDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.ClassMethodDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ClassMethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of ClassMethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.ClassMethodDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ClassMethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of ClassMethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CodeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.CodeType", "name": "CodeType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.CodeType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.CodeType", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CodeType.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.CodeType", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of CodeType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CodeType.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "types.CodeType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CodeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CodeType", "values": [], "variance": 0}}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.bytes", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.bytes", "builtins.bytes", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of CodeType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CodeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CodeType", "values": [], "variance": 0}]}}}, "co_argcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_argcount", "name": "co_argcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_argcount of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_argcount", "name": "co_argcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_argcount of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_cellvars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_cellvars", "name": "co_cellvars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_cellvars of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_cellvars", "name": "co_cellvars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_cellvars of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_code", "name": "co_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_code of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_code", "name": "co_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_code of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_consts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_consts", "name": "co_consts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_consts of CodeType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_consts", "name": "co_consts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_consts of CodeType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_exceptiontable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_exceptiontable", "name": "co_exceptiontable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_exceptiontable of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_exceptiontable", "name": "co_exceptiontable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_exceptiontable of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_filename", "name": "co_filename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_filename of CodeType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_filename", "name": "co_filename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_filename of CodeType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_firstlineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_firstlineno", "name": "co_firstlineno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_firstlineno of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_firstlineno", "name": "co_firstlineno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_firstlineno of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_flags", "name": "co_flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_flags of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_flags", "name": "co_flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_flags of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_freevars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_freevars", "name": "co_freevars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_freevars of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_freevars", "name": "co_freevars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_freevars of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_kwonlyargcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_kwonlyargcount", "name": "co_kwonlyargcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_kwonlyargcount of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_kwonlyargcount", "name": "co_kwonlyargcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_kwonlyargcount of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CodeType.co_lines", "name": "co_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_lines of CodeType", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "co_linetable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_linetable", "name": "co_linetable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_linetable of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_linetable", "name": "co_linetable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_linetable of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_lnotab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": "function types.CodeType.co_lnotab is deprecated: Will be removed in Python 3.14. Use the co_lines() method instead.", "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_lnotab", "name": "co_lnotab", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_lnotab of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_lnotab", "name": "co_lnotab", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_lnotab of CodeType", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_name", "name": "co_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_name of CodeType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_name", "name": "co_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_name of CodeType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_names", "name": "co_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_names of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_names", "name": "co_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_names of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_nlocals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_nlocals", "name": "co_nlocals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_nlocals of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_nlocals", "name": "co_nlocals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_nlocals of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CodeType.co_positions", "name": "co_positions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_positions of CodeType", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "co_posonlyargcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_posonlyargcount", "name": "co_posonlyargcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_posonlyargcount of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_posonlyargcount", "name": "co_posonlyargcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_posonlyargcount of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_qualname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_qualname", "name": "co_qualname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_qualname of CodeType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_qualname", "name": "co_qualname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_qualname of CodeType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_stacksize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_stacksize", "name": "co_stacksize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_stacksize of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_stacksize", "name": "co_stacksize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_stacksize of CodeType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "co_varnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CodeType.co_varnames", "name": "co_varnames", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_varnames of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CodeType.co_varnames", "name": "co_varnames", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.CodeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "co_varnames of CodeType", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "co_argcount", "co_posonlyargcount", "co_kwonlyargcount", "co_nlocals", "co_stacksize", "co_flags", "co_firstlineno", "co_code", "co_consts", "co_names", "co_varnames", "co_freevars", "co_cellvars", "co_filename", "co_name", "co_qualname", "co_linetable", "co_exceptiontable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CodeType.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "co_argcount", "co_posonlyargcount", "co_kwonlyargcount", "co_nlocals", "co_stacksize", "co_flags", "co_firstlineno", "co_code", "co_consts", "co_names", "co_varnames", "co_freevars", "co_cellvars", "co_filename", "co_name", "co_qualname", "co_linetable", "co_exceptiontable"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CodeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CodeType", "values": [], "variance": 0}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.bytes", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str", "builtins.str", "builtins.str", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace of CodeType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CodeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CodeType", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.CodeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.CodeType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CoroutineType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.CoroutineType", "name": "CoroutineType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.CoroutineType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "types", "mro": ["types.CoroutineType", "typing.Coroutine", "typing.Awaitable", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CoroutineType.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__await__ of CoroutineType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.CoroutineType.__name__", "name": "__name__", "type": "builtins.str"}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.CoroutineType.__qualname__", "name": "__qualname__", "type": "builtins.str"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CoroutineType.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of CoroutineType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cr_await": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CoroutineType.cr_await", "name": "cr_await", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_await of CoroutineType", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CoroutineType.cr_await", "name": "cr_await", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_await of CoroutineType", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cr_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CoroutineType.cr_code", "name": "cr_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_code of CoroutineType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CoroutineType.cr_code", "name": "cr_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_code of CoroutineType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cr_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CoroutineType.cr_frame", "name": "cr_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_frame of CoroutineType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CoroutineType.cr_frame", "name": "cr_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_frame of CoroutineType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cr_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CoroutineType.cr_origin", "name": "cr_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_origin of CoroutineType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CoroutineType.cr_origin", "name": "cr_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_origin of CoroutineType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cr_running": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CoroutineType.cr_running", "name": "cr_running", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_running of CoroutineType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CoroutineType.cr_running", "name": "cr_running", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_running of CoroutineType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cr_suspended": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.CoroutineType.cr_suspended", "name": "cr_suspended", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_suspended of CoroutineType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.CoroutineType.cr_suspended", "name": "cr_suspended", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cr_suspended of CoroutineType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.CoroutineType.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of CoroutineType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "throw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "types.CoroutineType.throw", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.CoroutineType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of CoroutineType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.CoroutineType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of CoroutineType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.CoroutineType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of CoroutineType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.CoroutineType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of CoroutineType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of CoroutineType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.CoroutineType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of CoroutineType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.CoroutineType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_YieldT_co", "_SendT_contra", "_ReturnT_co"], "typeddict_type": null}}, "DynamicClassAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "types.DynamicClassAttribute", "line": 619, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.property"}}, "EllipsisType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.EllipsisType", "name": "EllipsisType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.EllipsisType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.EllipsisType", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrameType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.FrameType", "name": "FrameType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.FrameType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.FrameType", "builtins.object"], "names": {".class": "SymbolTable", "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.FrameType.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of FrameType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "f_back": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FrameType.f_back", "name": "f_back", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_back of FrameType", "ret_type": {".class": "UnionType", "items": ["types.FrameType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FrameType.f_back", "name": "f_back", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_back of FrameType", "ret_type": {".class": "UnionType", "items": ["types.FrameType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "f_builtins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FrameType.f_builtins", "name": "f_builtins", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_builtins of FrameType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FrameType.f_builtins", "name": "f_builtins", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_builtins of FrameType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "f_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FrameType.f_code", "name": "f_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_code of FrameType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FrameType.f_code", "name": "f_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_code of FrameType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "f_globals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FrameType.f_globals", "name": "f_globals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_globals of FrameType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FrameType.f_globals", "name": "f_globals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_globals of FrameType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "f_lasti": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FrameType.f_lasti", "name": "f_lasti", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_lasti of FrameType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FrameType.f_lasti", "name": "f_lasti", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_lasti of FrameType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "f_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FrameType.f_lineno", "name": "f_lineno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_lineno of FrameType", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FrameType.f_lineno", "name": "f_lineno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_lineno of FrameType", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "f_locals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FrameType.f_locals", "name": "f_locals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_locals of FrameType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FrameType.f_locals", "name": "f_locals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FrameType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f_locals of FrameType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "f_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FrameType.f_trace", "name": "f_trace", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["types.FrameType", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "f_trace_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FrameType.f_trace_lines", "name": "f_trace_lines", "type": "builtins.bool"}}, "f_trace_opcodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FrameType.f_trace_opcodes", "name": "f_trace_opcodes", "type": "builtins.bool"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.FunctionType", "name": "FunctionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.FunctionType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.FunctionType", "builtins.object"], "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.FunctionType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["types.FunctionType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FunctionType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__closure__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FunctionType.__closure__", "name": "__closure__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__closure__ of FunctionType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["types.CellType"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FunctionType.__closure__", "name": "__closure__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__closure__ of FunctionType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["types.CellType"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__code__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__code__", "name": "__code__", "type": "types.CodeType"}}, "__defaults__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__defaults__", "name": "__defaults__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__dict__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__dict__", "name": "__dict__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "types.FunctionType.__get__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.FunctionType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["types.FunctionType", {".class": "NoneType"}, "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of FunctionType", "ret_type": "types.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.FunctionType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["types.FunctionType", {".class": "NoneType"}, "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of FunctionType", "ret_type": "types.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.FunctionType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.FunctionType", "builtins.object", {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of FunctionType", "ret_type": "types.MethodType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.FunctionType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.FunctionType", "builtins.object", {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of FunctionType", "ret_type": "types.MethodType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["types.FunctionType", {".class": "NoneType"}, "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of FunctionType", "ret_type": "types.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.FunctionType", "builtins.object", {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of FunctionType", "ret_type": "types.MethodType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__globals__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.FunctionType.__globals__", "name": "__globals__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__globals__ of FunctionType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.FunctionType.__globals__", "name": "__globals__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.FunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__globals__ of FunctionType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__kwdefaults__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__kwdefaults__", "name": "__kwdefaults__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__module__", "name": "__module__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__name__", "name": "__name__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["cls", "code", "globals", "name", "argdefs", "closure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "types.FunctionType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["cls", "code", "globals", "name", "argdefs", "closure"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.FunctionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.FunctionType", "values": [], "variance": 0}}, "types.CodeType", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["types.CellType"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of FunctionType", "ret_type": "types.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.FunctionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.FunctionType", "values": [], "variance": 0}]}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__qualname__", "name": "__qualname__", "type": "builtins.str"}}, "__type_params__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.FunctionType.__type_params__", "name": "__type_params__", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["typing.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.FunctionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.FunctionType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GeneratorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Generator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.GeneratorType", "name": "GeneratorType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.GeneratorType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "types", "mro": ["types.GeneratorType", "typing.Generator", "typing.Iterator", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GeneratorType.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.GeneratorType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of GeneratorType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.GeneratorType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, "values": [], "variance": 0}]}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.GeneratorType.__name__", "name": "__name__", "type": "builtins.str"}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GeneratorType.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.GeneratorType.__qualname__", "name": "__qualname__", "type": "builtins.str"}}, "gi_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GeneratorType.gi_code", "name": "gi_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_code of GeneratorType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GeneratorType.gi_code", "name": "gi_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_code of GeneratorType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gi_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GeneratorType.gi_frame", "name": "gi_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_frame of GeneratorType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GeneratorType.gi_frame", "name": "gi_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_frame of GeneratorType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gi_running": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GeneratorType.gi_running", "name": "gi_running", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_running of GeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GeneratorType.gi_running", "name": "gi_running", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_running of GeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gi_suspended": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GeneratorType.gi_suspended", "name": "gi_suspended", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_suspended of GeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GeneratorType.gi_suspended", "name": "gi_suspended", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_suspended of GeneratorType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gi_yieldfrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GeneratorType.gi_yieldfrom", "name": "gi_yieldfrom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_yieldfrom of GeneratorType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GeneratorType.gi_yieldfrom", "name": "gi_yieldfrom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gi_yieldfrom of GeneratorType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GeneratorType.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "throw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "types.GeneratorType.throw", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.GeneratorType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.GeneratorType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.GeneratorType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.GeneratorType.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, {".class": "TypeType", "item": "builtins.BaseException"}, {".class": "UnionType", "items": ["builtins.BaseException", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, "builtins.BaseException", {".class": "NoneType"}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of GeneratorType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.GeneratorType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "id": 1, "name": "_YieldT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "id": 2, "name": "_SendT_contra", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "id": 3, "name": "_ReturnT_co", "namespace": "types.GeneratorType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.GeneratorType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_YieldT_co", "_SendT_contra", "_ReturnT_co"], "typeddict_type": null}}, "GenericAlias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.GenericAlias", "name": "GenericAlias", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "types.GenericAlias", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.GenericAlias", "builtins.object"], "names": {".class": "SymbolTable", "__args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GenericAlias.__args__", "name": "__args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__args__ of GenericAlias", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GenericAlias.__args__", "name": "__args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__args__ of GenericAlias", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GenericAlias.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.GenericAlias", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of GenericAlias", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GenericAlias.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.GenericAlias", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of GenericAlias", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GenericAlias.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.GenericAlias", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of GenericAlias", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GenericAlias.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of GenericAlias", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "types.GenericAlias.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.GenericAlias.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.GenericAlias", "values": [], "variance": 0}}, "builtins.type", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of GenericAlias", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.GenericAlias.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.GenericAlias", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.GenericAlias.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.GenericAlias", "values": [], "variance": 0}]}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GenericAlias.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.GenericAlias", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or__ of GenericAlias", "ret_type": "types.UnionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__origin__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GenericAlias.__origin__", "name": "__origin__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__origin__ of GenericAlias", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GenericAlias.__origin__", "name": "__origin__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__origin__ of GenericAlias", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__parameters__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GenericAlias.__parameters__", "name": "__parameters__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__parameters__ of GenericAlias", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GenericAlias.__parameters__", "name": "__parameters__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__parameters__ of GenericAlias", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__ror__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GenericAlias.__ror__", "name": "__ror__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.GenericAlias", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ror__ of GenericAlias", "ret_type": "types.UnionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__typing_unpacked_tuple_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GenericAlias.__typing_unpacked_tuple_args__", "name": "__typing_unpacked_tuple_args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__typing_unpacked_tuple_args__ of GenericAlias", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GenericAlias.__typing_unpacked_tuple_args__", "name": "__typing_unpacked_tuple_args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__typing_unpacked_tuple_args__ of GenericAlias", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__unpacked__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GenericAlias.__unpacked__", "name": "__unpacked__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__unpacked__ of GenericAlias", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GenericAlias.__unpacked__", "name": "__unpacked__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GenericAlias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__unpacked__ of GenericAlias", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.GenericAlias.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.GenericAlias", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetSetDescriptorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.GetSetDescriptorType", "name": "GetSetDescriptorType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.GetSetDescriptorType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.GetSetDescriptorType", "builtins.object"], "names": {".class": "SymbolTable", "__delete__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GetSetDescriptorType.__delete__", "name": "__delete__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.GetSetDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delete__ of GetSetDescriptorType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GetSetDescriptorType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.GetSetDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of GetSetDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GetSetDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GetSetDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of GetSetDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GetSetDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GetSetDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of GetSetDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__objclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GetSetDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GetSetDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of GetSetDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GetSetDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GetSetDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of GetSetDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.GetSetDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GetSetDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of GetSetDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.GetSetDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.GetSetDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of GetSetDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.GetSetDescriptorType.__set__", "name": "__set__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["types.GetSetDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__set__ of GetSetDescriptorType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ItemsView": {".class": "SymbolTableNode", "cross_ref": "typing.ItemsView", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "KeysView": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LambdaType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "types.LambdaType", "line": 118, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "types.FunctionType"}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LoaderProtocol": {".class": "SymbolTableNode", "cross_ref": "_typeshed.importlib.LoaderProtocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MappingProxyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.MappingProxyType", "name": "MappingProxyType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.MappingProxyType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "types", "mro": ["types.MappingProxyType", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "types.MappingProxyType.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__class_getitem__ of MappingProxyType", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of MappingProxyType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of MappingProxyType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "types.MappingProxyType.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of MappingProxyType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "types.MappingProxyType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapping"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.MappingProxyType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_typeshed.SupportsKeysAndGetItem"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.MappingProxyType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, "values": [], "variance": 0}]}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T1", "id": -1, "name": "_T1", "namespace": "types.MappingProxyType.__or__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -2, "name": "_T2", "namespace": "types.MappingProxyType.__or__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or__ of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T1", "id": -1, "name": "_T1", "namespace": "types.MappingProxyType.__or__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -2, "name": "_T2", "namespace": "types.MappingProxyType.__or__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T1", "id": -1, "name": "_T1", "namespace": "types.MappingProxyType.__or__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -2, "name": "_T2", "namespace": "types.MappingProxyType.__or__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__reversed__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.__reversed__", "name": "__reversed__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reversed__ of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ror__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.__ror__", "name": "__ror__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T1", "id": -1, "name": "_T1", "namespace": "types.MappingProxyType.__ror__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -2, "name": "_T2", "namespace": "types.MappingProxyType.__ror__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ror__ of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T1", "id": -1, "name": "_T1", "namespace": "types.MappingProxyType.__ror__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -2, "name": "_T2", "namespace": "types.MappingProxyType.__ror__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T1", "id": -1, "name": "_T1", "namespace": "types.MappingProxyType.__ror__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -2, "name": "_T2", "namespace": "types.MappingProxyType.__ror__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.get", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.MappingProxyType.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MappingProxyType", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.MappingProxyType.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MappingProxyType", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.MappingProxyType.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MappingProxyType", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.MappingProxyType.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MappingProxyType", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MappingProxyType", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MappingProxyType", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "id": -1, "name": "_T2", "namespace": "types.MappingProxyType.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.ItemsView"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "<PERSON>.<PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MappingProxyType.values", "name": "values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "values of MappingProxyType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.ValuesView"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.MappingProxyType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "id": 1, "name": "_KT", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "id": 2, "name": "_VT_co", "namespace": "types.MappingProxyType", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_KT", "_VT_co"], "typeddict_type": null}}, "MaybeNone": {".class": "SymbolTableNode", "cross_ref": "_typeshed.MaybeNone", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MemberDescriptorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.MemberDescriptorType", "name": "MemberDescriptorType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.MemberDescriptorType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.MemberDescriptorType", "builtins.object"], "names": {".class": "SymbolTable", "__delete__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MemberDescriptorType.__delete__", "name": "__delete__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.MemberDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delete__ of MemberDescriptorType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MemberDescriptorType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.MemberDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of MemberDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MemberDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MemberDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MemberDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MemberDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MemberDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MemberDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__objclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MemberDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MemberDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of MemberDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MemberDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MemberDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of MemberDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MemberDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MemberDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MemberDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MemberDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MemberDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MemberDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MemberDescriptorType.__set__", "name": "__set__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["types.MemberDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__set__ of MemberDescriptorType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MethodDescriptorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.MethodDescriptorType", "name": "MethodDescriptorType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.MethodDescriptorType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.MethodDescriptorType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodDescriptorType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["types.MethodDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MethodDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodDescriptorType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.MethodDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of MethodDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__objclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of MethodDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of MethodDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MethodDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MethodType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.MethodType", "name": "MethodType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.MethodType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.MethodType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["types.MethodType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MethodType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__closure__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodType.__closure__", "name": "__closure__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__closure__ of MethodType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["types.CellType"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodType.__closure__", "name": "__closure__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__closure__ of MethodType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["types.CellType"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__code__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodType.__code__", "name": "__code__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__code__ of MethodType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodType.__code__", "name": "__code__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__code__ of MethodType", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__defaults__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodType.__defaults__", "name": "__defaults__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__defaults__ of MethodType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodType.__defaults__", "name": "__defaults__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__defaults__ of MethodType", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodType.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.MethodType", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of MethodType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__func__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodType.__func__", "name": "__func__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__func__ of MethodType", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodType.__func__", "name": "__func__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__func__ of MethodType", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodType.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of MethodType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MethodType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MethodType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "types.MethodType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.MethodType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.MethodType", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of MethodType", "ret_type": "types.MethodType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.MethodType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.MethodType", "values": [], "variance": 0}]}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MethodType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MethodType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__self__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodType.__self__", "name": "__self__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__self__ of MethodType", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodType.__self__", "name": "__self__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__self__ of MethodType", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.MethodType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.MethodType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MethodWrapperType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.MethodWrapperType", "name": "MethodWrapperType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.MethodWrapperType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.MethodWrapperType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodWrapperType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["types.MethodWrapperType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MethodWrapperType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodWrapperType.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.MethodWrapperType", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of MethodWrapperType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodWrapperType.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of MethodWrapperType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodWrapperType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MethodWrapperType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodWrapperType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of MethodWrapperType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.MethodWrapperType.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.MethodWrapperType", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of MethodWrapperType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__objclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodWrapperType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of MethodWrapperType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodWrapperType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of MethodWrapperType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodWrapperType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MethodWrapperType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodWrapperType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of MethodWrapperType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__self__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.MethodWrapperType.__self__", "name": "__self__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__self__ of MethodWrapperType", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.MethodWrapperType.__self__", "name": "__self__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.MethodWrapperType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__self__ of MethodWrapperType", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModuleSpec": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.ModuleSpec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.ModuleType", "name": "ModuleType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "types.ModuleType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.ModuleType", "builtins.object"], "names": {".class": "SymbolTable", "__dict__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.ModuleType.__dict__", "name": "__dict__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__dict__ of ModuleType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.ModuleType.__dict__", "name": "__dict__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__dict__ of ModuleType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.ModuleType.__doc__", "name": "__doc__", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__file__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.ModuleType.__file__", "name": "__file__", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.ModuleType.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.ModuleType", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of ModuleType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.ModuleType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "doc"], "arg_types": ["types.ModuleType", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ModuleType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__loader__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.ModuleType.__loader__", "name": "__loader__", "type": {".class": "UnionType", "items": ["_typeshed.importlib.LoaderProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.ModuleType.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.ModuleType.__package__", "name": "__package__", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__path__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.ModuleType.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableSequence"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.ModuleType.__spec__", "name": "__spec__", "type": {".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoneType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.NoneType", "name": "NoneType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.NoneType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.NoneType", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.NoneType.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.NoneType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of NoneType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotImplementedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "types.NotImplementedType", "line": 665, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins._NotImplementedType"}}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SimpleNamespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.SimpleNamespace", "name": "SimpleNamespace", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "types.SimpleNamespace", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.SimpleNamespace", "builtins.object"], "names": {".class": "SymbolTable", "__delattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.SimpleNamespace.__delattr__", "name": "__delattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.SimpleNamespace", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delattr__ of SimpleNamespace", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.SimpleNamespace.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.SimpleNamespace", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of SimpleNamespace", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattribute__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.SimpleNamespace.__getattribute__", "name": "__getattribute__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.SimpleNamespace", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattribute__ of SimpleNamespace", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "types.SimpleNamespace.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.SimpleNamespace.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["types.SimpleNamespace", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleNamespace", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.SimpleNamespace.__setattr__", "name": "__setattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["types.SimpleNamespace", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setattr__ of SimpleNamespace", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsKeysAndGetItem": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsKeysAndGetItem", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.TracebackType", "name": "TracebackType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.TracebackType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.TracebackType", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "tb_next", "tb_frame", "tb_lasti", "tb_lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "types.TracebackType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "tb_next", "tb_frame", "tb_lasti", "tb_lineno"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.TracebackType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.TracebackType", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "types.FrameType", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of TracebackType", "ret_type": "types.TracebackType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.TracebackType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.TracebackType", "values": [], "variance": 0}]}}}, "tb_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.TracebackType.tb_frame", "name": "tb_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tb_frame of TracebackType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.TracebackType.tb_frame", "name": "tb_frame", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tb_frame of TracebackType", "ret_type": "types.FrameType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tb_lasti": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.TracebackType.tb_lasti", "name": "tb_lasti", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tb_lasti of TracebackType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.TracebackType.tb_lasti", "name": "tb_lasti", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tb_lasti of TracebackType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tb_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.TracebackType.tb_lineno", "name": "tb_lineno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tb_lineno of TracebackType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.TracebackType.tb_lineno", "name": "tb_lineno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tb_lineno of TracebackType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tb_next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "types.TracebackType.tb_next", "name": "tb_next", "type": {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types.TracebackType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "types.TracebackType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVarTuple": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVarTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UnionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.UnionType", "name": "UnionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.UnionType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.UnionType", "builtins.object"], "names": {".class": "SymbolTable", "__args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.UnionType.__args__", "name": "__args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.UnionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__args__ of UnionType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.UnionType.__args__", "name": "__args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.UnionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__args__ of UnionType", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.UnionType.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.UnionType", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of UnionType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.UnionType.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.UnionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of UnionType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__or__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.UnionType.__or__", "name": "__or__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.UnionType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__or__ of UnionType", "ret_type": "types.UnionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ror__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.UnionType.__ror__", "name": "__ror__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["types.UnionType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ror__ of UnionType", "ret_type": "types.UnionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValuesView": {".class": "SymbolTableNode", "cross_ref": "typing.ValuesView", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WrapperDescriptorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "types.WrapperDescriptorType", "name": "WrapperDescriptorType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "types.WrapperDescriptorType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "types", "mro": ["types.WrapperDescriptorType", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.WrapperDescriptorType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["types.WrapperDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of WrapperDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.WrapperDescriptorType.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["types.WrapperDescriptorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WrapperDescriptorType", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.WrapperDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.WrapperDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of WrapperDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.WrapperDescriptorType.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.WrapperDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of WrapperDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__objclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.WrapperDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.WrapperDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of WrapperDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.WrapperDescriptorType.__objclass__", "name": "__objclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.WrapperDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__objclass__ of WrapperDescriptorType", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "types.WrapperDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.WrapperDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of WrapperDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "types.WrapperDescriptorType.__qualname__", "name": "__qualname__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["types.WrapperDescriptorType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__qualname__ of WrapperDescriptorType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Fn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "name": "_Fn", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "_KT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._KT", "name": "_KT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_NotImplementedType": {".class": "SymbolTableNode", "cross_ref": "builtins._NotImplementedType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_P": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._P", "name": "_P", "upper_bound": "builtins.object", "variance": 0}}, "_R": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "name": "_R", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_ReturnT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._ReturnT_co", "name": "_ReturnT_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "_SendT_contra": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._SendT_contra", "name": "_SendT_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "_T1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T1", "name": "_T1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._T2", "name": "_T2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_VT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._VT_co", "name": "_VT_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "_YieldT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._YieldT_co", "name": "_YieldT_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "types.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "types.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "types.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "coroutine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "types.coroutine", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.coroutine", "name": "coroutine", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coroutine", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.coroutine", "name": "coroutine", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coroutine", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "types.coroutine", "name": "coroutine", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coroutine", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "types.coroutine", "name": "coroutine", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coroutine", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coroutine", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "types._P", "id": -1, "name": "_P", "namespace": "types.coroutine#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._R", "id": -2, "name": "_R", "namespace": "types.coroutine#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coroutine", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "types._Fn", "id": -1, "name": "_Fn", "namespace": "types.coroutine", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}]}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_original_bases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.get_original_bases", "name": "get_original_bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_original_bases", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["name", "bases", "kwds", "exec_body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.new_class", "name": "new_class", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["name", "bases", "kwds", "exec_body"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_class", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "prepare_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["name", "bases", "kwds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.prepare_class", "name": "prepare_class", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["name", "bases", "kwds"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_class", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.type", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_bases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bases"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "types.resolve_bases", "name": "resolve_bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bases"], "arg_types": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_bases", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "e:\\code\\VSCode-win32-x64-1.101.1\\data\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\types.pyi"}