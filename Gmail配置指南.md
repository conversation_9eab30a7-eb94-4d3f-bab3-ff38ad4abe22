# Gmail SMTP 配置指南

## 🎯 问题诊断结果

✅ **网络连接正常** - 所有SMTP服务器都可连接  
❌ **认证配置问题** - 需要正确设置Gmail认证

## 📧 Gmail SMTP 正确配置步骤

### 1. 启用两步验证
1. 访问 [Google账户安全设置](https://myaccount.google.com/security)
2. 找到"两步验证"
3. 点击"开始使用"并完成设置

### 2. 生成应用专用密码
1. 在安全设置页面，找到"应用专用密码"
2. 选择"邮件"应用类型
3. 选择设备类型（如"Windows电脑"）
4. 点击"生成"
5. **复制生成的16位密码**（格式：abcd efgh ijkl mnop）

### 3. CMS邮件服务配置
```
配置名称: Gmail配置
SMTP服务器: smtp.gmail.com
端口: 587
使用SSL/TLS: ❌ 不勾选（使用STARTTLS）
用户名: <EMAIL>
密码: [16位应用专用密码]
发件邮箱: <EMAIL>
发件人名称: 您的网站名称
测试邮箱: 任何有效邮箱地址
当前生效配置: ✅ 勾选
```

## ⚠️ 常见错误

### 错误1：使用普通密码
❌ 使用Gmail登录密码  
✅ 使用应用专用密码

### 错误2：SSL设置错误
❌ 端口587 + SSL勾选  
✅ 端口587 + SSL不勾选（STARTTLS）

### 错误3：账户安全设置
❌ 未启用两步验证  
✅ 必须启用两步验证才能生成应用专用密码

## 🔧 替代方案

### QQ邮箱配置
```
SMTP服务器: smtp.qq.com
端口: 587
使用SSL/TLS: ❌ 不勾选
用户名: <EMAIL>
密码: QQ邮箱授权码（不是QQ密码）
```

### 163邮箱配置
```
SMTP服务器: smtp.163.com
端口: 25 或 465
使用SSL/TLS: 根据端口选择
用户名: <EMAIL>
密码: 邮箱授权码
```

## 🧪 测试步骤

1. 按照上述步骤配置Gmail
2. 重启CMS服务器
3. 进入"邮件服务配置"
4. 添加Gmail配置
5. 点击"测试邮件配置"按钮
6. 检查测试邮件是否发送成功

## 💡 故障排除

如果仍然失败，检查：
1. 应用专用密码是否正确复制
2. Gmail账户是否正常
3. 两步验证是否已启用
4. 是否有其他安全软件干扰

## 📞 技术支持

如果问题仍然存在，请提供：
1. 具体的错误信息
2. 使用的邮箱类型
3. 配置截图（隐藏敏感信息）
