import logging
import smtplib
import ssl
from email.mime.text import MIMEText

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_smtp_connection():
    """测试SMTP连接和认证"""
    # 邮件配置
    mail_server = "mail9.serv00.com"
    mail_port = 587
    mail_username = "<EMAIL>"
    mail_password = "Sty94025@"
    use_tls = True
    use_ssl = False
    sender = mail_username
    recipient = "<EMAIL>"

    # 打印邮件配置
    logger.info("邮件配置:")
    logger.info(f"邮件服务器: {mail_server}")
    logger.info(f"端口: {mail_port}")
    logger.info(f"使用TLS: {use_tls}")
    logger.info(f"使用SSL: {use_ssl}")
    logger.info(f"用户名: {mail_username}")
    logger.info(f"密码: {'*' * 8}")

    try:
        logger.info("尝试连接到SMTP服务器...")

        if use_ssl:
            logger.info("使用SSL连接")
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(mail_server, mail_port, context=context)
        else:
            logger.info("使用普通连接")
            server = smtplib.SMTP(mail_server, mail_port)

            if use_tls:
                logger.info("启用TLS")
                server.starttls()

        # 设置调试级别
        server.set_debuglevel(1)

        logger.info("尝试登录...")
        server.login(mail_username, mail_password)
        logger.info("登录成功!")

        # 尝试发送一封简单的邮件
        logger.info("尝试发送测试邮件...")

        # 使用简单的纯文本邮件
        msg = MIMEText("这是一封简单的测试邮件，请回复确认收到。", "plain", "utf-8")
        msg["Subject"] = "简单测试邮件"
        msg["From"] = sender
        msg["To"] = recipient

        # 发送邮件
        server.sendmail(sender, [recipient], msg.as_string())
        logger.info("测试邮件发送成功!")

        # 关闭连接
        server.quit()
        logger.info("SMTP连接已关闭")

        return True
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
        import traceback

        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    test_smtp_connection()
