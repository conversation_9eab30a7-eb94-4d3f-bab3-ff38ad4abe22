#!/usr/bin/env python3
"""
独立的异步邮件发送测试脚本

本测试验证以下功能：
1. 真正的异步邮件发送（立即响应，邮件后台发送）
2. 多配置重试机制（自动尝试最佳TLS配置）
3. 智能错误处理（失败时自动切换配置）
4. 完全独立运行，不依赖FastAPI项目启动

多配置重试机制说明：
- 配置1: STARTTLS (推荐) - use_tls=False, start_tls=True (适用于端口587)
- 配置2: SSL直连 - use_tls=True, start_tls=False (适用于端口465)
- 配置3: 无加密连接 - use_tls=False, start_tls=False (适用于内网环境)

系统会按优先级自动尝试，直到找到可用的配置。
这确保了在不同SMTP环境下都能正常工作。
"""

# ==================== 邮箱配置区域 ====================
# 在这里修改邮箱相关配置，方便统一管理

# SMTP服务器配置
DEFAULT_SMTP_HOST = "mail9.serv00.com"
DEFAULT_SMTP_PORT = 587
DEFAULT_SMTP_USER = "<EMAIL>"
DEFAULT_SMTP_PASSWORD = "Sty94025@"

# 发件人信息
DEFAULT_EMAIL_FROM = "<EMAIL>"
DEFAULT_EMAIL_FROM_NAME = "MetaTrader Auth Support"

# 测试邮箱（接收测试邮件的邮箱）
TEST_EMAIL = "<EMAIL>"

# ====================================================




import asyncio
import os
import sys
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from pathlib import Path

import aiosmtplib

# 添加项目根目录到 Python 路径（用于加载配置）
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class IndependentEmailService:
    """独立的邮件发送服务"""
    
    def __init__(self):
        # 从环境变量或.env文件加载配置
        self.load_config()
    
    def load_config(self):
        """加载邮件配置"""
        try:
            # 尝试加载.env文件
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            print("⚠️ python-dotenv 未安装，使用系统环境变量")
        
        # 读取配置（使用顶部定义的默认值）
        self.smtp_host = os.getenv("SMTP_HOST", DEFAULT_SMTP_HOST)
        self.smtp_port = int(os.getenv("SMTP_PORT", str(DEFAULT_SMTP_PORT)))
        self.smtp_username = os.getenv("SMTP_USER", DEFAULT_SMTP_USER)
        self.smtp_password = os.getenv("SMTP_PASSWORD", DEFAULT_SMTP_PASSWORD)
        self.email_from = os.getenv("EMAILS_FROM_EMAIL", DEFAULT_EMAIL_FROM)
        self.email_from_name = os.getenv("MAIL_FROM_NAME", DEFAULT_EMAIL_FROM_NAME)
        
        # TLS配置
        smtp_tls = os.getenv("SMTP_TLS", "true").lower() == "true"
        smtp_ssl = os.getenv("SMTP_SSL", "false").lower() == "true"
        
        # 根据环境变量设置推断最佳配置
        if self.smtp_port == 587 and smtp_tls:
            self.preferred_config = "STARTTLS"
        elif self.smtp_port == 465 and smtp_ssl:
            self.preferred_config = "SSL"
        else:
            self.preferred_config = "STARTTLS"  # 默认
    
    def print_config(self):
        """打印当前配置"""
        print("📧 邮件服务配置")
        print("=" * 50)
        print(f"  SMTP主机: {self.smtp_host}")
        print(f"  SMTP端口: {self.smtp_port}")
        print(f"  SMTP用户: {self.smtp_username}")
        print(f"  发件人: {self.email_from}")
        print(f"  发件人名称: {self.email_from_name}")
        print(f"  推荐配置: {self.preferred_config}")
        print("=" * 50)
    
    async def send_email_async(self, to_email: str, subject: str, content: str) -> tuple[bool, str, float]:
        """
        异步发送邮件
        
        Returns:
            tuple: (成功状态, 使用的配置, 发送时间)
        """
        start_time = time.time()
        
        # 创建邮件
        message = MIMEMultipart("alternative")
        message["From"] = f"{self.email_from_name} <{self.email_from}>"
        message["To"] = to_email
        message["Subject"] = subject
        
        # 添加HTML内容
        html_part = MIMEText(content, "html", "utf-8")
        message.attach(html_part)
        
        # 定义多种TLS配置
        tls_configs = [
            {
                "name": "STARTTLS (推荐)",
                "use_tls": False,
                "start_tls": True,
                "description": "普通连接 + STARTTLS升级，适用于端口587",
            },
            {
                "name": "SSL直连",
                "use_tls": True,
                "start_tls": False,
                "description": "SSL直接连接，适用于端口465",
            },
            {
                "name": "无加密连接",
                "use_tls": False,
                "start_tls": False,
                "description": "无加密连接，适用于内网或测试环境",
            },
        ]
        
        # 尝试每种配置
        for i, config in enumerate(tls_configs, 1):
            try:
                print(f"🔧 尝试配置 {i}/{len(tls_configs)}: {config['name']}")
                print(f"   {config['description']}")
                
                await aiosmtplib.send(
                    message,
                    hostname=self.smtp_host,
                    port=self.smtp_port,
                    username=self.smtp_username,
                    password=self.smtp_password,
                    use_tls=config["use_tls"],
                    start_tls=config["start_tls"],
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"✅ 邮件发送成功！使用配置: {config['name']}")
                return True, config['name'], duration
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ 配置 {config['name']} 失败: {error_msg}")
                
                if i < len(tls_configs):
                    print("🔄 尝试下一个配置...")
                else:
                    print("💥 所有配置都失败了")
        
        end_time = time.time()
        duration = end_time - start_time
        return False, "所有配置失败", duration


async def test_async_email_sending():
    """测试异步邮件发送"""
    print("🎯 独立异步邮件发送测试")
    print("=" * 60)
    
    # 创建邮件服务
    email_service = IndependentEmailService()
    email_service.print_config()
    
    # 测试邮箱（使用顶部定义的测试邮箱）
    test_email = TEST_EMAIL
    print(f"\n📧 测试邮箱: {test_email}")
    
    # 创建验证码邮件内容
    verification_code = "999999"
    subject = f"[{email_service.email_from_name}] 独立测试验证码"
    
    html_content = f"""
    <html>
    <body>
        <h2>邮箱验证码</h2>
        <p>您的验证码是：<strong style="font-size: 24px; color: #007bff;">{verification_code}</strong></p>
        <p>验证码有效期：15分钟</p>
        <p>这是一封独立测试邮件，验证异步邮件发送功能。</p>
        <hr>
        <p><small>来自 {email_service.email_from_name}</small></p>
    </body>
    </html>
    """
    
    print(f"🔢 验证码: {verification_code}")
    print("📤 开始异步邮件发送测试...")
    
    # 记录开始时间
    total_start_time = time.time()
    
    # 异步发送邮件
    success, config_used, send_duration = await email_service.send_email_async(
        test_email, subject, html_content
    )
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 结果分析
    print("\n" + "=" * 60)
    print("📊 测试结果分析")
    print("=" * 60)
    print(f"📧 目标邮箱: {test_email}")
    print(f"✅ 发送状态: {'成功' if success else '失败'}")
    print(f"🔧 使用配置: {config_used}")
    print(f"⏱️  发送时间: {send_duration:.2f}秒")
    print(f"⏱️  总时间: {total_duration:.2f}秒")
    
    # 异步性能评估
    if success:
        if total_duration < 1.0:
            performance = "🎉 优秀"
        elif total_duration < 3.0:
            performance = "👍 良好"
        elif total_duration < 5.0:
            performance = "⚠️ 一般"
        else:
            performance = "❌ 较差"
        
        print(f"🚀 异步性能: {performance}")
        
        if "STARTTLS" in config_used:
            print("🎯 确认使用 STARTTLS 配置！")
        
        print(f"\n💌 请检查您的邮箱 {test_email}")
        print(f"📧 邮件主题: {subject}")
        print(f"🔢 验证码: {verification_code}")
    else:
        print("❌ 邮件发送失败，请检查配置")
    
    print("=" * 60)
    return success


def main():
    """主函数"""
    print("🧪 独立SMTP异步测试工具")
    print("🔧 完全独立运行，无需启动FastAPI项目")
    print("=" * 60)
    
    try:
        # 运行异步测试
        success = asyncio.run(test_async_email_sending())
        
        if success:
            print("\n🎉 测试完成！邮件发送成功")
        else:
            print("\n❌ 测试失败！请检查配置")
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
