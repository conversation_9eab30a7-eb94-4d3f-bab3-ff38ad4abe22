#!/usr/bin/env node

// 高级邮件测试脚本 - 尝试不同的TLS配置
const nodemailer = require('nodemailer')

console.log('🧪 高级邮件配置测试...')

// 不同的TLS配置选项
const tlsConfigs = [
  {
    name: '标准TLS',
    tls: {
      rejectUnauthorized: false
    }
  },
  {
    name: '宽松TLS',
    tls: {
      rejectUnauthorized: false,
      ciphers: 'SSLv3'
    }
  },
  {
    name: '兼容TLS',
    tls: {
      rejectUnauthorized: false,
      secureProtocol: 'TLSv1_method'
    }
  },
  {
    name: '无TLS验证',
    tls: {
      rejectUnauthorized: false,
      checkServerIdentity: () => undefined
    }
  }
]

// 测试配置
const baseConfigs = [
  {
    name: 'Gmail STARTTLS',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false
  },
  {
    name: 'Gmail SSL',
    host: 'smtp.gmail.com',
    port: 465,
    secure: true
  },
  {
    name: 'QQ邮箱 STARTTLS',
    host: 'smtp.qq.com',
    port: 587,
    secure: false
  },
  {
    name: 'QQ邮箱 SSL',
    host: 'smtp.qq.com',
    port: 465,
    secure: true
  },
  {
    name: '163邮箱',
    host: 'smtp.163.com',
    port: 25,
    secure: false
  },
  {
    name: 'Outlook',
    host: 'smtp-mail.outlook.com',
    port: 587,
    secure: false
  }
]

async function testConfigWithTLS(baseConfig, tlsConfig) {
  try {
    const transporter = nodemailer.createTransport({
      host: baseConfig.host,
      port: baseConfig.port,
      secure: baseConfig.secure,
      ...tlsConfig,
      // 不使用认证，只测试连接
      auth: undefined
    })
    
    // 只验证连接
    await transporter.verify()
    return true
    
  } catch (error) {
    // 如果是认证错误，说明连接成功了
    if (error.code === 'EAUTH' || error.message.includes('535') || error.message.includes('authentication')) {
      return true // 连接成功，只是认证失败
    }
    return false
  }
}

async function main() {
  console.log('开始测试不同的TLS配置组合...\n')
  
  const successfulConfigs = []
  
  for (const baseConfig of baseConfigs) {
    console.log(`\n📧 测试服务: ${baseConfig.name}`)
    console.log(`服务器: ${baseConfig.host}:${baseConfig.port}`)
    
    let foundWorking = false
    
    for (const tlsConfig of tlsConfigs) {
      process.stdout.write(`  🔄 ${tlsConfig.name}... `)
      
      const success = await testConfigWithTLS(baseConfig, tlsConfig)
      
      if (success) {
        console.log('✅ 成功!')
        successfulConfigs.push({
          ...baseConfig,
          ...tlsConfig,
          configName: `${baseConfig.name} (${tlsConfig.name})`
        })
        foundWorking = true
        break // 找到一个可用的就停止
      } else {
        console.log('❌ 失败')
      }
    }
    
    if (!foundWorking) {
      console.log(`  ❌ ${baseConfig.name} - 所有TLS配置都失败`)
    }
  }
  
  console.log('\n' + '='.repeat(60))
  console.log('📊 测试结果总结')
  console.log('='.repeat(60))
  
  if (successfulConfigs.length === 0) {
    console.log('❌ 没有找到可用的邮件服务配置')
    console.log('\n可能的问题:')
    console.log('1. 网络防火墙阻止SMTP端口')
    console.log('2. ISP阻止邮件端口')
    console.log('3. 本地安全软件干扰')
    console.log('4. 代理服务器问题')
  } else {
    console.log(`✅ 找到 ${successfulConfigs.length} 个可用配置:\n`)
    
    successfulConfigs.forEach((config, index) => {
      console.log(`${index + 1}. ${config.configName}`)
      console.log(`   服务器: ${config.host}:${config.port}`)
      console.log(`   安全连接: ${config.secure ? 'SSL' : 'STARTTLS'}`)
      console.log(`   TLS配置: ${JSON.stringify(config.tls, null, 2)}`)
      console.log('')
    })
    
    console.log('🎯 推荐配置 (复制到CMS中):')
    const recommended = successfulConfigs[0]
    console.log(`
SMTP服务器: ${recommended.host}
端口: ${recommended.port}
使用SSL/TLS: ${recommended.secure ? '勾选' : '不勾选'}
TLS配置: ${JSON.stringify(recommended.tls)}
    `)
  }
}

main().catch(console.error)
