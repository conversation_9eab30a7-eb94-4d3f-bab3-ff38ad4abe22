#!/usr/bin/env node

// 综合邮件测试脚本 - 测试所有可能的邮件服务配置
const nodemailer = require('nodemailer')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

// 预定义的邮件服务配置
const EMAIL_SERVICES = {
  1: {
    name: 'Gmail (STARTTLS)',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    requiresAuth: true,
    description: '需要应用专用密码',
  },
  2: {
    name: 'Gmail (SSL)',
    host: 'smtp.gmail.com',
    port: 465,
    secure: true,
    requiresAuth: true,
    description: '需要应用专用密码',
  },
  3: {
    name: 'QQ邮箱 (STARTTLS)',
    host: 'smtp.qq.com',
    port: 587,
    secure: false,
    requiresAuth: true,
    description: '需要授权码',
  },
  4: {
    name: 'QQ邮箱 (SSL)',
    host: 'smtp.qq.com',
    port: 465,
    secure: true,
    requiresAuth: true,
    description: '需要授权码',
  },
  5: {
    name: '163邮箱 (非加密)',
    host: 'smtp.163.com',
    port: 25,
    secure: false,
    requiresAuth: true,
    description: '需要授权码',
  },
  6: {
    name: '163邮箱 (SSL)',
    host: 'smtp.163.com',
    port: 465,
    secure: true,
    requiresAuth: true,
    description: '需要授权码',
  },
  7: {
    name: 'Outlook (STARTTLS)',
    host: 'smtp-mail.outlook.com',
    port: 587,
    secure: false,
    requiresAuth: true,
    description: '使用普通密码',
  },
  8: {
    name: 'Serv00 (STARTTLS)',
    host: 'mail9.serv00.com',
    port: 587,
    secure: false,
    requiresAuth: true,
    description: '您的Serv00配置',
  },
  9: {
    name: 'Serv00 (SSL)',
    host: 'mail9.serv00.com',
    port: 465,
    secure: true,
    requiresAuth: true,
    description: '您的Serv00配置',
  },
  10: {
    name: '自定义配置',
    host: '',
    port: 587,
    secure: false,
    requiresAuth: true,
    description: '手动输入配置',
  },
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

async function testEmailConfiguration(config, credentials) {
  console.log(`\n🔄 测试配置: ${config.name}`)
  console.log(`服务器: ${config.host}:${config.port}`)
  console.log(`安全连接: ${config.secure ? 'SSL' : 'STARTTLS'}`)

  try {
    // 创建传输器
    const transportConfig = {
      host: config.host,
      port: config.port,
      secure: config.secure,
      tls: {
        rejectUnauthorized: false,
        ciphers: 'SSLv3',
      },
    }

    if (config.requiresAuth && credentials.username && credentials.password) {
      transportConfig.auth = {
        user: credentials.username,
        pass: credentials.password,
      }
    }

    const transporter = nodemailer.createTransport(transportConfig)

    // 验证连接
    console.log('🔍 验证SMTP连接...')
    await transporter.verify()
    console.log('✅ SMTP连接验证成功!')

    // 发送测试邮件
    if (credentials.testEmail) {
      console.log('📤 发送测试邮件...')
      const info = await transporter.sendMail({
        from: `"测试发件人" <${credentials.username}>`,
        to: credentials.testEmail,
        subject: `邮件测试 - ${config.name} - ${new Date().toLocaleString()}`,
        html: `
          <h2>🎉 邮件配置测试成功！</h2>
          <p>恭喜！您的邮件配置工作正常。</p>
          <hr>
          <p><strong>配置信息:</strong></p>
          <ul>
            <li>服务: ${config.name}</li>
            <li>服务器: ${config.host}:${config.port}</li>
            <li>安全连接: ${config.secure ? 'SSL' : 'STARTTLS'}</li>
            <li>测试时间: ${new Date().toLocaleString()}</li>
          </ul>
          <p style="color: green;"><strong>✅ 此配置可以在CMS中使用！</strong></p>
        `,
        text: `
邮件配置测试成功！

配置信息:
- 服务: ${config.name}
- 服务器: ${config.host}:${config.port}
- 安全连接: ${config.secure ? 'SSL' : 'STARTTLS'}
- 测试时间: ${new Date().toLocaleString()}

✅ 此配置可以在CMS中使用！
        `,
      })

      console.log(`✅ 测试邮件发送成功! 消息ID: ${info.messageId}`)
      console.log(`📧 邮件已发送到: ${credentials.testEmail}`)

      return {
        success: true,
        config: config,
        credentials: credentials,
        messageId: info.messageId,
      }
    } else {
      console.log('✅ SMTP连接测试成功! (未发送邮件)')
      return {
        success: true,
        config: config,
        credentials: credentials,
        messageId: null,
      }
    }
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`)

    // 提供具体的错误解决建议
    if (error.code === 'EAUTH') {
      console.log('💡 认证失败解决方案:')
      if (config.host.includes('gmail.com')) {
        console.log('  - 确保已启用Gmail两步验证')
        console.log('  - 使用应用专用密码，不是普通密码')
        console.log('  - 访问: https://myaccount.google.com/apppasswords')
      } else if (config.host.includes('qq.com')) {
        console.log('  - 使用QQ邮箱授权码，不是QQ密码')
        console.log('  - 在QQ邮箱设置中开启SMTP服务')
      } else {
        console.log('  - 检查用户名和密码是否正确')
        console.log('  - 确认邮箱服务已启用SMTP')
      }
    } else if (error.code === 'ECONNECTION') {
      console.log('💡 连接失败解决方案:')
      console.log('  - 检查网络连接')
      console.log('  - 检查防火墙设置')
      console.log('  - 尝试其他端口配置')
    }

    return {
      success: false,
      config: config,
      error: error.message,
      errorCode: error.code,
    }
  }
}

async function main() {
  console.log('🧪 综合邮件服务测试工具')
  console.log('=' * 50)
  console.log('这个工具将帮助您找到可用的邮件配置\n')

  // 显示可用的邮件服务
  console.log('📋 可用的邮件服务:')
  Object.entries(EMAIL_SERVICES).forEach(([key, service]) => {
    console.log(`${key.padStart(2)}. ${service.name} - ${service.description}`)
  })

  const successfulConfigs = []

  while (true) {
    console.log('\n' + '=' * 50)
    const choice = await question('\n请选择要测试的邮件服务 (输入数字，或 q 退出): ')

    if (choice.toLowerCase() === 'q') {
      break
    }

    const service = EMAIL_SERVICES[choice]
    if (!service) {
      console.log('❌ 无效选择，请重试')
      continue
    }

    let config = { ...service }

    // 如果是自定义配置，获取用户输入
    if (choice === '10') {
      config.host = await question('请输入SMTP服务器地址: ')
      config.port = parseInt(await question('请输入端口号 (25/465/587): '))
      const secureChoice = await question('使用SSL? (y/n): ')
      config.secure = secureChoice.toLowerCase() === 'y'
      config.name = `自定义 ${config.host}:${config.port}`
    }

    // 获取认证信息
    const credentials = {}
    if (config.requiresAuth) {
      credentials.username = await question('请输入邮箱地址: ')
      credentials.password = await question('请输入密码/授权码: ')
      credentials.testEmail = await question('请输入测试邮件接收地址 (可选，直接回车跳过): ')
    }

    // 执行测试
    const result = await testEmailConfiguration(config, credentials)

    if (result.success) {
      successfulConfigs.push(result)
      console.log('\n🎉 测试成功! 此配置已保存到成功列表')
    }

    const continueTest = await question('\n是否继续测试其他配置? (y/n): ')
    if (continueTest.toLowerCase() !== 'y') {
      break
    }
  }

  // 显示测试结果总结
  console.log('\n' + '=' * 60)
  console.log('📊 测试结果总结')
  console.log('=' * 60)

  if (successfulConfigs.length === 0) {
    console.log('❌ 没有找到可用的邮件配置')
    console.log('\n💡 建议:')
    console.log('1. 检查网络连接和防火墙设置')
    console.log('2. 确认邮箱密码/授权码正确')
    console.log('3. 尝试不同的邮件服务提供商')
  } else {
    console.log(`✅ 找到 ${successfulConfigs.length} 个可用配置:\n`)

    successfulConfigs.forEach((result, index) => {
      console.log(`${index + 1}. ${result.config.name}`)
      console.log(`   服务器: ${result.config.host}:${result.config.port}`)
      console.log(`   安全连接: ${result.config.secure ? 'SSL' : 'STARTTLS'}`)
      console.log(`   用户名: ${result.credentials.username}`)
      if (result.messageId) {
        console.log(`   ✅ 邮件发送成功 (ID: ${result.messageId})`)
      } else {
        console.log(`   ✅ 连接验证成功`)
      }
      console.log('')
    })

    console.log('🎯 推荐在CMS中使用第一个成功的配置!')
  }

  rl.close()
}

if (require.main === module) {
  main().catch(console.error)
}
