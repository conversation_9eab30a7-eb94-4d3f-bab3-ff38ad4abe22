// 运行：node dbm.cjs

// 数据库配置变量 - 硬编码主数据库连接信息
const DB_CONFIG = {
  // PostgreSQL 服务器配置
  host: '127.0.0.1',
  port: 5432,
  superUser: 'postgres',
  superPassword: 'c83a350cfb60',
  defaultDatabase: 'postgres',
}
/*
//远程配置
const DB_CONFIG = {
  // PostgreSQL 服务器配置
  host: '***************',
  port: 54321,
  superUser: 'user_ySNrDb',
  superPassword: 'password_sAn83b',
  defaultDatabase: 'postgres',
}
*/
// 依赖检查和自动安装函数
function checkAndInstallDependencies() {
  // 定义各种数据库驱动包
  const databaseDrivers = {
    postgresql: ['pg'],
    mysql: ['mysql2'],
    mariadb: ['mysql2', 'mariadb'],
    sqlite: ['sqlite3'],
    mongodb: ['mongodb'],
    mssql: ['mssql'],
    oracle: ['oracledb'],
  }

  // 基础必需包
  const basePackages = ['pg'] // 默认支持 PostgreSQL

  // 检测项目中使用的数据库类型
  let detectedDrivers = new Set(basePackages)

  try {
    // 检查 package.json 中的数据库相关依赖
    const packageJsonPath = require('path').join(process.cwd(), 'package.json')
    if (require('fs').existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(require('fs').readFileSync(packageJsonPath, 'utf8'))
      const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies }

      // 检测 Payload CMS 数据库适配器
      Object.keys(allDeps).forEach((dep) => {
        if (dep.includes('@payloadcms/db-postgres')) {
          databaseDrivers.postgresql.forEach((pkg) => detectedDrivers.add(pkg))
        } else if (dep.includes('@payloadcms/db-mysql')) {
          databaseDrivers.mysql.forEach((pkg) => detectedDrivers.add(pkg))
        } else if (dep.includes('@payloadcms/db-sqlite')) {
          databaseDrivers.sqlite.forEach((pkg) => detectedDrivers.add(pkg))
        } else if (dep.includes('@payloadcms/db-mongodb')) {
          databaseDrivers.mongodb.forEach((pkg) => detectedDrivers.add(pkg))
        }

        // 检测直接的数据库驱动依赖
        Object.values(databaseDrivers)
          .flat()
          .forEach((driver) => {
            if (dep === driver) {
              detectedDrivers.add(driver)
            }
          })
      })
    }
  } catch (error) {
    console.log('⚠️  无法读取 package.json，使用默认配置')
  }

  // 检查环境变量中的数据库配置
  try {
    const envPath = require('path').join(process.cwd(), '.env')
    if (require('fs').existsSync(envPath)) {
      const envContent = require('fs').readFileSync(envPath, 'utf8')

      if (envContent.includes('mysql://') || envContent.includes('DATABASE_URL=mysql')) {
        databaseDrivers.mysql.forEach((pkg) => detectedDrivers.add(pkg))
      }
      if (envContent.includes('postgres://') || envContent.includes('postgresql://')) {
        databaseDrivers.postgresql.forEach((pkg) => detectedDrivers.add(pkg))
      }
      if (
        envContent.includes('sqlite://') ||
        envContent.includes('.db') ||
        envContent.includes('.sqlite')
      ) {
        databaseDrivers.sqlite.forEach((pkg) => detectedDrivers.add(pkg))
      }
      if (envContent.includes('mongodb://') || envContent.includes('mongo://')) {
        databaseDrivers.mongodb.forEach((pkg) => detectedDrivers.add(pkg))
      }
    }
  } catch (error) {
    console.log('⚠️  无法读取 .env 文件')
  }

  console.log(`🔍 检测到需要的数据库驱动: ${Array.from(detectedDrivers).join(', ')}`)

  // 安装检测到的驱动包
  for (const pkg of detectedDrivers) {
    try {
      require.resolve(pkg)
      console.log(`✅ 依赖 "${pkg}" 已存在`)
    } catch (error) {
      console.log(`⚠️  依赖 "${pkg}" 未找到，正在安装...`)
      try {
        const { execSync } = require('child_process')
        // 检查是否使用 pnpm
        const packageManager = require('fs').existsSync('pnpm-lock.yaml')
          ? 'pnpm'
          : require('fs').existsSync('yarn.lock')
            ? 'yarn'
            : 'npm'

        console.log(`📦 使用 ${packageManager} 安装 ${pkg}...`)
        execSync(`${packageManager} ${packageManager === 'yarn' ? 'add' : 'install'} ${pkg}`, {
          stdio: 'inherit',
          cwd: process.cwd(),
        })
        console.log(`✅ 依赖 "${pkg}" 安装成功`)
      } catch (installError) {
        console.error(`❌ 安装依赖 "${pkg}" 失败:`, installError.message)
        console.log(`💡 请手动运行: npm install ${pkg}`)
        // 不退出程序，继续尝试其他包
      }
    }
  }

  console.log('🎉 依赖检查完成！')
}

// 在导入其他模块之前先检查依赖
checkAndInstallDependencies()

const { Client } = require('pg')
const { execSync } = require('child_process')
const readline = require('readline')
const fs = require('fs')
const path = require('path')

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function colorLog(message, color = 'reset') {
  console.log(colors[color] + message + colors.reset)
}

// 询问用户输入
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// 配置文件路径
const CONFIG_FILE_PATH = path.join(__dirname, '.dbm-config.json')

// 读取配置文件
function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE_PATH)) {
      const configData = JSON.parse(fs.readFileSync(CONFIG_FILE_PATH, 'utf8'))

      // 更新配置
      Object.keys(configData).forEach((key) => {
        if (DB_CONFIG.hasOwnProperty(key)) {
          DB_CONFIG[key] = configData[key]
        }
      })

      colorLog('✅ 已从配置文件加载设置', 'green')
      return true
    }
  } catch (error) {
    colorLog(`⚠️  读取配置文件失败: ${error.message}`, 'yellow')
  }
  return false
}

// 保存配置到文件
function saveConfig() {
  try {
    // 创建一个不包含敏感密码的配置副本
    const configToSave = { ...DB_CONFIG }

    // 询问是否保存密码
    const saveSensitiveData = process.env.DBM_SAVE_PASSWORDS === 'true'

    // 如果不保存敏感数据，则清除密码
    if (!saveSensitiveData) {
      delete configToSave.superPassword
      delete configToSave.databasePassword
    }

    fs.writeFileSync(CONFIG_FILE_PATH, JSON.stringify(configToSave, null, 2), 'utf8')
    colorLog('✅ 配置已保存到文件', 'green')
    return true
  } catch (error) {
    colorLog(`⚠️  保存配置文件失败: ${error.message}`, 'yellow')
    return false
  }
}

// 修改数据库配置函数已在其他地方定义

// 显示菜单
function showMenu() {
  console.clear()
  colorLog('╔══════════════════════════════════════════════════════╗', 'cyan')
  colorLog('║                数据库管理工具                        ║', 'cyan')
  colorLog('╠══════════════════════════════════════════════════════╣', 'cyan')
  colorLog('║                                                      ║', 'cyan')
  colorLog('║  1. 🔍 数据库连接检测                                ║', 'cyan')
  colorLog('║  2. 🚀 数据库初始化                                  ║', 'cyan')
  colorLog('║  3. 🔄 数据库重建                                    ║', 'cyan')
  colorLog('║  4. 📦 运行数据库迁移                                ║', 'cyan')
  colorLog('║  5. 🗑️  删除数据库                                   ║', 'cyan')
  colorLog('║  6. 🆕 创建新数据库                                  ║', 'cyan')
  colorLog('║  7. ⚙️  修改数据库配置                               ║', 'cyan')
  colorLog('║  0. 🚪 退出                                          ║', 'cyan')
  colorLog('║                                                      ║', 'cyan')
  colorLog('╚══════════════════════════════════════════════════════╝', 'cyan')
  console.log()
}

// 数据库连接检测
async function testConnection() {
  colorLog('\n🔍 正在检测数据库连接...', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 数据库连接成功！', 'green')

    colorLog('\n📋 数据库连接信息:', 'cyan')
    colorLog(`🏠 主机地址: ${DB_CONFIG.host}:${DB_CONFIG.port}`, 'bright')
    colorLog(`👑 超级用户: ${DB_CONFIG.superUser}`, 'bright')
    colorLog(`🗄️  默认数据库: ${DB_CONFIG.defaultDatabase}`, 'bright')

    // 列出所有用户数据库
    const allDbResult = await client.query(`
      SELECT datname 
      FROM pg_database 
      WHERE datname NOT IN ('template0', 'template1', 'postgres')
      ORDER BY datname
    `)

    if (allDbResult.rows.length > 0) {
      colorLog('\n📋 服务器上的数据库列表:', 'cyan')
      allDbResult.rows.forEach((row, index) => {
        const dbName = row.datname
        colorLog(`   ${(index + 1).toString().padStart(2)}. ${dbName}`, 'bright')
      })
    } else {
      colorLog('\n⚠️ 未发现任何用户数据库', 'yellow')
    }
  } catch (error) {
    colorLog(`❌ 数据库连接失败: ${error.message}`, 'red')
    return false
  } finally {
    await client.end()
  }

  return true
}

// 数据库初始化
async function initializeDatabase() {
  colorLog('\n🚀 数据库初始化', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 已连接到 PostgreSQL', 'green')

    // 列出所有用户数据库
    const allDbResult = await client.query(`
      SELECT datname 
      FROM pg_database 
      WHERE datname NOT IN ('template0', 'template1', 'postgres')
      ORDER BY datname
    `)

    if (allDbResult.rows.length === 0) {
      colorLog('⚠️  未找到任何用户数据库', 'yellow')
      await client.end()
      return
    }

    colorLog('\n📋 选择要初始化的数据库:', 'cyan')
    allDbResult.rows.forEach((row, index) => {
      colorLog(`${(index + 1).toString().padStart(2)}. ${row.datname}`, 'bright')
    })

    const dbChoice = await askQuestion('\n请选择数据库编号 (0 取消): ')
    if (dbChoice === '0') {
      colorLog('操作已取消', 'yellow')
      await client.end()
      return
    }

    const dbIndex = parseInt(dbChoice) - 1
    if (isNaN(dbIndex) || dbIndex < 0 || dbIndex >= allDbResult.rows.length) {
      colorLog('❌ 无效的选择', 'red')
      await client.end()
      return
    }

    const selectedDb = allDbResult.rows[dbIndex].datname

    // 检查用户是否存在
    const userResult = await client.query(
      `
      SELECT usename 
      FROM pg_user 
      WHERE usename = $1
    `,
      [selectedDb],
    )

    let dbUser = selectedDb
    let dbPassword = ''

    if (userResult.rows.length === 0) {
      colorLog(`⚠️  未找到与数据库同名的用户，需要创建新用户`, 'yellow')

      // 获取用户名
      dbUser = (await askQuestion(`请输入数据库用户名 (默认: ${selectedDb}): `)) || selectedDb

      // 获取密码
      while (!dbPassword) {
        dbPassword = await askQuestion(`请为用户 "${dbUser}" 设置密码: `)
        if (!dbPassword) {
          colorLog('❌ 密码不能为空', 'red')
        }
      }

      // 创建用户
      try {
        await client.query(`CREATE USER ${dbUser} WITH PASSWORD '${dbPassword}'`)
        colorLog(`✅ 已创建用户 "${dbUser}"`, 'green')

        // 授予权限
        await client.query(`GRANT ALL PRIVILEGES ON DATABASE ${selectedDb} TO ${dbUser}`)
        colorLog(`✅ 已授予数据库权限给 "${dbUser}"`, 'green')
      } catch (error) {
        colorLog(`⚠️  创建用户失败: ${error.message}`, 'yellow')
      }
    } else {
      colorLog(`✅ 找到用户 "${dbUser}"`, 'green')

      // 询问是否重置密码
      const resetPassword = await askQuestion(`是否重置用户 "${dbUser}" 的密码? (y/N): `)
      if (resetPassword.toLowerCase() === 'y' || resetPassword.toLowerCase() === 'yes') {
        while (!dbPassword) {
          dbPassword = await askQuestion(`请为用户 "${dbUser}" 设置新密码: `)
          if (!dbPassword) {
            colorLog('❌ 密码不能为空', 'red')
          }
        }

        try {
          await client.query(`ALTER USER ${dbUser} WITH PASSWORD '${dbPassword}'`)
          colorLog(`✅ 已更新用户 "${dbUser}" 的密码`, 'green')
        } catch (error) {
          colorLog(`⚠️  更新密码失败: ${error.message}`, 'yellow')
        }
      } else {
        // 如果不重置密码，则需要手动输入现有密码以生成连接字符串
        dbPassword = await askQuestion(`请输入用户 "${dbUser}" 的现有密码 (用于生成连接字符串): `)
        if (!dbPassword) {
          colorLog('⚠️  未提供密码，将使用空密码生成连接字符串', 'yellow')
          dbPassword = ''
        }
      }
    }

    // 连接到目标数据库设置 schema 权限
    try {
      const dbClient = new Client({
        host: DB_CONFIG.host,
        port: DB_CONFIG.port,
        user: DB_CONFIG.superUser,
        password: DB_CONFIG.superPassword,
        database: selectedDb,
      })

      await dbClient.connect()
      await dbClient.query(`GRANT ALL ON SCHEMA public TO ${dbUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${dbUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${dbUser}`)
      colorLog(`✅ 已设置 schema 权限`, 'green')
      await dbClient.end()
    } catch (error) {
      colorLog(`⚠️  设置 schema 权限失败: ${error.message}`, 'yellow')
    }

    // 生成连接字符串
    const connectionString = `postgres://${dbUser}:${dbPassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${selectedDb}`
    colorLog('\n🔗 数据库连接字符串:', 'cyan')
    colorLog(`${connectionString}`, 'yellow')

    // 询问是否更新环境变量
    const updateEnv = await askQuestion('\n是否更新/添加环境变量? (Y/n): ')
    if (updateEnv.toLowerCase() !== 'n' && updateEnv.toLowerCase() !== 'no') {
      try {
        const envPath = path.join(process.cwd(), '.env')
        let envContent = ''

        if (fs.existsSync(envPath)) {
          envContent = fs.readFileSync(envPath, 'utf8')

          // 检查是否已存在 DATABASE_URI
          if (envContent.includes('DATABASE_URI=')) {
            // 替换现有的 DATABASE_URI
            envContent = envContent.replace(/DATABASE_URI=.*/, `DATABASE_URI=${connectionString}`)
            colorLog('✅ 已更新环境变量 DATABASE_URI', 'green')
          } else {
            // 添加新的 DATABASE_URI
            envContent += `\nDATABASE_URI=${connectionString}\n`
            colorLog('✅ 已添加环境变量 DATABASE_URI', 'green')
          }
        } else {
          // 创建新的 .env 文件
          envContent = `DATABASE_URI=${connectionString}\n`
          colorLog('✅ 已创建 .env 文件并添加环境变量 DATABASE_URI', 'green')
        }

        fs.writeFileSync(envPath, envContent)
      } catch (error) {
        colorLog(`❌ 更新环境变量失败: ${error.message}`, 'red')
      }
    }

    colorLog('\n🎉 数据库初始化完成！', 'green')
  } catch (error) {
    colorLog(`❌ 数据库初始化失败: ${error.message}`, 'red')
    throw error
  } finally {
    if (client.connected) {
      await client.end().catch(() => {})
    }
  }
}

// 数据库重建
async function rebuildDatabase() {
  colorLog('\n🔄 数据库重建', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 已连接到 PostgreSQL', 'green')

    // 列出所有用户数据库
    const allDbResult = await client.query(`
      SELECT datname 
      FROM pg_database 
      WHERE datname NOT IN ('template0', 'template1', 'postgres')
      ORDER BY datname
    `)

    if (allDbResult.rows.length === 0) {
      colorLog('⚠️  未找到任何用户数据库', 'yellow')
      await client.end()
      return
    }

    colorLog('\n📋 选择要重建的数据库:', 'cyan')
    allDbResult.rows.forEach((row, index) => {
      colorLog(`${(index + 1).toString().padStart(2)}. ${row.datname}`, 'bright')
    })

    const dbChoice = await askQuestion('\n请选择数据库编号 (0 取消): ')
    if (dbChoice === '0') {
      colorLog('操作已取消', 'yellow')
      await client.end()
      return
    }

    const dbIndex = parseInt(dbChoice) - 1
    if (isNaN(dbIndex) || dbIndex < 0 || dbIndex >= allDbResult.rows.length) {
      colorLog('❌ 无效的选择', 'red')
      await client.end()
      return
    }

    const dbToRebuild = allDbResult.rows[dbIndex].datname

    // 检查用户是否存在
    const userResult = await client.query(`SELECT usename FROM pg_user WHERE usename = $1`, [
      dbToRebuild,
    ])
    const dbUser = userResult.rows.length > 0 ? userResult.rows[0].usename : dbToRebuild

    // 警告确认
    colorLog(`\n⚠️  警告：即将重建数据库 "${dbToRebuild}"`, 'red')
    colorLog('⚠️  此操作将删除数据库中的所有数据，并创建一个全新的空数据库！', 'red')
    colorLog('⚠️  所有表、数据和关联都将被永久删除！', 'red')

    const confirm = await askQuestion('\n请输入数据库名称以确认重建: ')
    if (confirm !== dbToRebuild) {
      colorLog('❌ 操作已取消：确认信息不匹配', 'yellow')
      await client.end()
      return
    }

    // 获取密码（如果用户已存在）
    let dbPassword = ''
    if (userResult.rows.length > 0) {
      dbPassword = await askQuestion(`请输入用户 "${dbUser}" 的密码 (如需重置密码请留空): `)

      // 如果密码为空，则重置密码
      if (!dbPassword) {
        // 生成随机密码
        const generateRandomPassword = () => {
          const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
          let password = ''
          for (let i = 0; i < 16; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length))
          }
          return password
        }

        const defaultPassword = generateRandomPassword()
        colorLog(`🔑 已生成随机密码: ${defaultPassword}`, 'yellow')

        dbPassword =
          (await askQuestion(`请输入新密码 (默认使用生成的随机密码): `)) || defaultPassword
      }
    } else {
      // 如果用户不存在，需要创建用户
      colorLog(`⚠️  未找到与数据库同名的用户，将创建新用户`, 'yellow')

      // 生成随机密码
      const generateRandomPassword = () => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
        let password = ''
        for (let i = 0; i < 16; i++) {
          password += chars.charAt(Math.floor(Math.random() * chars.length))
        }
        return password
      }

      const defaultPassword = generateRandomPassword()
      colorLog(`🔑 已生成随机密码: ${defaultPassword}`, 'yellow')

      dbPassword =
        (await askQuestion(`请输入数据库密码 (默认使用生成的随机密码): `)) || defaultPassword
    }

    // 开始重建
    colorLog('\n🔄 开始重建数据库...', 'cyan')

    // 断开所有连接
    try {
      await client.query(
        `
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = $1 AND pid <> pg_backend_pid()
      `,
        [dbToRebuild],
      )
      colorLog('✅ 已断开所有现有连接', 'green')
    } catch (error) {
      colorLog(`⚠️  断开连接时出现问题: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }

    // 删除数据库
    try {
      await client.query(`DROP DATABASE IF EXISTS ${dbToRebuild}`)
      colorLog(`✅ 已删除数据库 "${dbToRebuild}"`, 'green')
    } catch (error) {
      colorLog(`❌ 删除数据库失败: ${error.message}`, 'red')
      await client.end()
      return
    }

    // 创建新数据库
    try {
      await client.query(`CREATE DATABASE ${dbToRebuild}`)
      colorLog(`✅ 已重新创建数据库 "${dbToRebuild}"`, 'green')
    } catch (error) {
      colorLog(`❌ 创建数据库失败: ${error.message}`, 'red')
      await client.end()
      return
    }

    // 处理用户
    if (userResult.rows.length > 0) {
      // 更新现有用户密码
      try {
        await client.query(`ALTER USER ${dbUser} WITH PASSWORD '${dbPassword}'`)
        colorLog(`✅ 已更新用户 "${dbUser}" 的密码`, 'green')
      } catch (error) {
        colorLog(`⚠️  更新用户密码失败: ${error.message}`, 'yellow')
      }
    } else {
      // 创建新用户
      try {
        await client.query(`CREATE USER ${dbUser} WITH PASSWORD '${dbPassword}'`)
        colorLog(`✅ 已创建用户 "${dbUser}"`, 'green')
      } catch (error) {
        colorLog(`⚠️  创建用户失败: ${error.message}`, 'yellow')
      }
    }

    // 授予权限
    try {
      await client.query(`GRANT ALL PRIVILEGES ON DATABASE ${dbToRebuild} TO ${dbUser}`)
      colorLog(`✅ 已授予数据库权限给 "${dbUser}"`, 'green')
    } catch (error) {
      colorLog(`⚠️  授权失败: ${error.message}`, 'yellow')
    }

    // 设置 schema 权限
    try {
      const dbClient = new Client({
        host: DB_CONFIG.host,
        port: DB_CONFIG.port,
        user: DB_CONFIG.superUser,
        password: DB_CONFIG.superPassword,
        database: dbToRebuild,
      })

      await dbClient.connect()
      await dbClient.query(`GRANT ALL ON SCHEMA public TO ${dbUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${dbUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${dbUser}`)
      colorLog(`✅ 已设置 schema 权限`, 'green')
      await dbClient.end()
    } catch (error) {
      colorLog(`⚠️  设置 schema 权限失败: ${error.message}`, 'yellow')
    }

    // 生成连接字符串
    const connectionString = `postgres://${dbUser}:${dbPassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${dbToRebuild}`
    colorLog('\n🔗 数据库连接字符串:', 'cyan')
    colorLog(`${connectionString}`, 'yellow')

    // 询问是否更新环境变量
    const updateEnv = await askQuestion('\n是否更新/添加环境变量? (Y/n): ')
    if (updateEnv.toLowerCase() !== 'n' && updateEnv.toLowerCase() !== 'no') {
      try {
        const envPath = path.join(process.cwd(), '.env')
        let envContent = ''

        if (fs.existsSync(envPath)) {
          envContent = fs.readFileSync(envPath, 'utf8')

          // 检查是否已存在 DATABASE_URI
          if (envContent.includes('DATABASE_URI=')) {
            // 替换现有的 DATABASE_URI
            envContent = envContent.replace(/DATABASE_URI=.*/, `DATABASE_URI=${connectionString}`)
            colorLog('✅ 已更新环境变量 DATABASE_URI', 'green')
          } else {
            // 添加新的 DATABASE_URI
            envContent += `\nDATABASE_URI=${connectionString}\n`
            colorLog('✅ 已添加环境变量 DATABASE_URI', 'green')
          }
        } else {
          // 创建新的 .env 文件
          envContent = `DATABASE_URI=${connectionString}\n`
          colorLog('✅ 已创建 .env 文件并添加环境变量 DATABASE_URI', 'green')
        }

        fs.writeFileSync(envPath, envContent)
      } catch (error) {
        colorLog(`❌ 更新环境变量失败: ${error.message}`, 'red')
      }
    }

    colorLog('\n🎉 数据库重建完成！', 'green')
  } catch (error) {
    colorLog(`❌ 数据库重建失败: ${error.message}`, 'red')
  } finally {
    if (client.connected) {
      await client.end().catch(() => {})
    }
  }
}

// 运行数据库迁移
async function runMigrations() {
  colorLog('\n📦 运行数据库迁移', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 已连接到 PostgreSQL', 'green')

    // 列出所有用户数据库
    const allDbResult = await client.query(`
      SELECT datname 
      FROM pg_database 
      WHERE datname NOT IN ('template0', 'template1', 'postgres')
      ORDER BY datname
    `)

    if (allDbResult.rows.length === 0) {
      colorLog('⚠️  未找到任何用户数据库', 'yellow')
      await client.end()
      return
    }

    colorLog('\n📋 选择要运行迁移的数据库:', 'cyan')
    allDbResult.rows.forEach((row, index) => {
      colorLog(`${(index + 1).toString().padStart(2)}. ${row.datname}`, 'bright')
    })

    const dbChoice = await askQuestion('\n请选择数据库编号 (0 取消): ')
    if (dbChoice === '0') {
      colorLog('操作已取消', 'yellow')
      await client.end()
      return
    }

    const dbIndex = parseInt(dbChoice) - 1
    if (isNaN(dbIndex) || dbIndex < 0 || dbIndex >= allDbResult.rows.length) {
      colorLog('❌ 无效的选择', 'red')
      await client.end()
      return
    }

    const selectedDb = allDbResult.rows[dbIndex].datname

    // 检查用户是否存在
    const userResult = await client.query(
      `
      SELECT usename 
      FROM pg_user 
      WHERE usename = $1
    `,
      [selectedDb],
    )

    let dbUser = selectedDb
    let dbPassword = ''

    if (userResult.rows.length === 0) {
      colorLog(`⚠️  未找到与数据库同名的用户，需要提供连接信息`, 'yellow')

      // 获取用户名
      dbUser = (await askQuestion(`请输入数据库用户名 (默认: ${selectedDb}): `)) || selectedDb

      // 获取密码
      dbPassword = await askQuestion(`请输入用户 "${dbUser}" 的密码: `)
    } else {
      colorLog(`✅ 找到用户 "${dbUser}"`, 'green')

      // 获取密码
      dbPassword = await askQuestion(`请输入用户 "${dbUser}" 的密码: `)
    }

    await client.end()

    // 生成连接字符串
    const connectionString = `postgres://${dbUser}:${dbPassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${selectedDb}`

    // 更新环境变量
    const envPath = path.join(process.cwd(), '.env')
    let envContent = ''
    let originalEnv = ''

    if (fs.existsSync(envPath)) {
      originalEnv = fs.readFileSync(envPath, 'utf8')
      envContent = originalEnv

      // 检查是否已存在 DATABASE_URI
      if (envContent.includes('DATABASE_URI=')) {
        // 临时替换现有的 DATABASE_URI
        envContent = envContent.replace(/DATABASE_URI=.*/, `DATABASE_URI=${connectionString}`)
      } else {
        // 添加新的 DATABASE_URI
        envContent += `\nDATABASE_URI=${connectionString}\n`
      }
    } else {
      // 创建临时的 .env 文件
      envContent = `DATABASE_URI=${connectionString}\n`
    }

    // 写入临时环境变量
    fs.writeFileSync(envPath, envContent)
    colorLog(`✅ 已临时更新环境变量以运行迁移`, 'green')

    // 警告确认
    colorLog(`\n⚠️  警告：即将对数据库 "${selectedDb}" 运行迁移`, 'yellow')
    colorLog('⚠️  此操作可能会修改数据库结构，请确保已备份重要数据', 'yellow')

    const confirm = await askQuestion('\n确认运行迁移? (y/N): ')
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      colorLog('❌ 操作已取消', 'yellow')

      // 恢复原始环境变量
      if (originalEnv) {
        fs.writeFileSync(envPath, originalEnv)
        colorLog('✅ 已恢复原始环境变量', 'green')
      }

      return
    }

    // 运行迁移
    colorLog('\n🚀 开始运行数据库迁移...', 'cyan')

    try {
      // 检测包管理器
      const packageManager = fs.existsSync('pnpm-lock.yaml')
        ? 'pnpm'
        : fs.existsSync('yarn.lock')
          ? 'yarn'
          : 'npm'

      execSync(`${packageManager} payload migrate`, { stdio: 'inherit', cwd: process.cwd() })
      colorLog('✅ 数据库迁移完成！', 'green')

      // 询问是否保留环境变量更改
      const keepEnvChanges = await askQuestion('\n是否保留环境变量更改? (Y/n): ')
      if (keepEnvChanges.toLowerCase() === 'n' || keepEnvChanges.toLowerCase() === 'no') {
        // 恢复原始环境变量
        if (originalEnv) {
          fs.writeFileSync(envPath, originalEnv)
          colorLog('✅ 已恢复原始环境变量', 'green')
        }
      } else {
        colorLog('✅ 已保留环境变量更改', 'green')
      }
    } catch (error) {
      colorLog(`❌ 数据库迁移失败: ${error.message}`, 'red')

      // 恢复原始环境变量
      if (originalEnv) {
        fs.writeFileSync(envPath, originalEnv)
        colorLog('✅ 已恢复原始环境变量', 'green')
      }
    }
  } catch (error) {
    colorLog(`❌ 数据库迁移失败: ${error.message}`, 'red')
  }
}

// 更新 .env 文件
function updateEnvFile() {
  try {
    const envPath = path.join(process.cwd(), '.env')
    let envContent = fs.readFileSync(envPath, 'utf8')

    const newDatabaseUri = `postgres://${DB_CONFIG.databaseUser}:${DB_CONFIG.databasePassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.databaseName}`

    envContent = envContent.replace(/DATABASE_URI=.*/, `DATABASE_URI=${newDatabaseUri}`)

    fs.writeFileSync(envPath, envContent)
    colorLog('✅ 已更新 .env 文件', 'green')
  } catch (error) {
    colorLog(`⚠️  更新 .env 文件时出现问题: ${error.message}`, 'yellow')
  }
}

// 修改数据库配置
async function modifyDatabaseConfig() {
  colorLog('\n⚙️  修改数据库配置', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  // 显示当前配置
  colorLog('\n📋 当前数据库连接信息:', 'cyan')
  colorLog(`🏠 主机地址: ${DB_CONFIG.host}:${DB_CONFIG.port}`, 'bright')
  colorLog(`👑 超级用户: ${DB_CONFIG.superUser}`, 'bright')
  colorLog(`🗄️  默认数据库: ${DB_CONFIG.defaultDatabase}`, 'bright')

  // 列出所有环境变量中的数据库连接
  try {
    const envPath = path.join(process.cwd(), '.env')
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8')
      const dbConnections = envContent.match(/DATABASE_URI=.*/g) || []

      if (dbConnections.length > 0) {
        colorLog('\n📄 .env 文件中的数据库连接:', 'cyan')
        dbConnections.forEach((conn, index) => {
          colorLog(`${index + 1}. ${conn}`, 'yellow')
        })
      } else {
        colorLog('\n⚠️  .env 文件中未找到数据库连接配置', 'yellow')
      }
    } else {
      colorLog('\n⚠️  未找到 .env 文件', 'yellow')
    }
  } catch (error) {
    colorLog(`❌ 读取 .env 文件失败: ${error.message}`, 'red')
  }

  // 询问是否修改主数据库配置
  const modifyMain = await askQuestion('\n是否修改主数据库连接信息? (y/N): ')
  if (modifyMain.toLowerCase() === 'y' || modifyMain.toLowerCase() === 'yes') {
    colorLog('\n📡 修改 PostgreSQL 服务器配置:', 'cyan')

    const host = await askQuestion(`主机地址 (默认: ${DB_CONFIG.host}): `)
    if (host) DB_CONFIG.host = host

    const port = await askQuestion(`端口 (默认: ${DB_CONFIG.port}): `)
    if (port) DB_CONFIG.port = parseInt(port)

    const superUser = await askQuestion(`超级用户 (默认: ${DB_CONFIG.superUser}): `)
    if (superUser) DB_CONFIG.superUser = superUser

    const superPassword = await askQuestion(`超级用户密码: `)
    if (superPassword) DB_CONFIG.superPassword = superPassword

    const defaultDatabase = await askQuestion(`默认数据库 (默认: ${DB_CONFIG.defaultDatabase}): `)
    if (defaultDatabase) DB_CONFIG.defaultDatabase = defaultDatabase

    colorLog('\n✅ 主数据库连接信息已更新', 'green')
  }

  // 询问是否更新环境变量
  const updateEnv = await askQuestion('\n是否更新/添加环境变量中的数据库连接? (y/N): ')
  if (updateEnv.toLowerCase() === 'y' || updateEnv.toLowerCase() === 'yes') {
    // 列出所有用户数据库
    const client = new Client({
      host: DB_CONFIG.host,
      port: DB_CONFIG.port,
      user: DB_CONFIG.superUser,
      password: DB_CONFIG.superPassword,
      database: DB_CONFIG.defaultDatabase,
    })

    try {
      await client.connect()

      const allDbResult = await client.query(`
        SELECT datname 
        FROM pg_database 
        WHERE datname NOT IN ('template0', 'template1', 'postgres')
        ORDER BY datname
      `)

      if (allDbResult.rows.length === 0) {
        colorLog('\n⚠️  未找到任何用户数据库', 'yellow')
        await client.end()
        return
      }

      colorLog('\n📋 选择要配置的数据库:', 'cyan')
      allDbResult.rows.forEach((row, index) => {
        colorLog(`${(index + 1).toString().padStart(2)}. ${row.datname}`, 'bright')
      })

      const dbChoice = await askQuestion('\n请选择数据库编号 (0 取消): ')
      if (dbChoice === '0') {
        colorLog('操作已取消', 'yellow')
        await client.end()
        return
      }

      const dbIndex = parseInt(dbChoice) - 1
      if (isNaN(dbIndex) || dbIndex < 0 || dbIndex >= allDbResult.rows.length) {
        colorLog('❌ 无效的选择', 'red')
        await client.end()
        return
      }

      const selectedDb = allDbResult.rows[dbIndex].datname

      // 查询用户信息
      const userResult = await client.query(
        `
        SELECT usename 
        FROM pg_user 
        WHERE usename = $1
      `,
        [selectedDb],
      )

      let dbUser = selectedDb
      let dbPassword = ''

      if (userResult.rows.length === 0) {
        colorLog(`⚠️  未找到与数据库同名的用户，需要创建新用户`, 'yellow')

        // 获取用户名
        dbUser = (await askQuestion(`请输入数据库用户名 (默认: ${selectedDb}): `)) || selectedDb

        // 获取密码
        while (!dbPassword) {
          dbPassword = await askQuestion(`请为用户 "${dbUser}" 设置密码: `)
          if (!dbPassword) {
            colorLog('❌ 密码不能为空', 'red')
          }
        }

        // 创建用户
        try {
          await client.query(`CREATE USER ${dbUser} WITH PASSWORD '${dbPassword}'`)
          colorLog(`✅ 已创建用户 "${dbUser}"`, 'green')

          // 授予权限
          await client.query(`GRANT ALL PRIVILEGES ON DATABASE ${selectedDb} TO ${dbUser}`)
          colorLog(`✅ 已授予数据库权限给 "${dbUser}"`, 'green')
        } catch (error) {
          colorLog(`⚠️  创建用户失败: ${error.message}`, 'yellow')
        }
      } else {
        colorLog(`✅ 找到用户 "${dbUser}"`, 'green')

        // 询问是否重置密码
        const resetPassword = await askQuestion(`是否重置用户 "${dbUser}" 的密码? (y/N): `)
        if (resetPassword.toLowerCase() === 'y' || resetPassword.toLowerCase() === 'yes') {
          while (!dbPassword) {
            dbPassword = await askQuestion(`请为用户 "${dbUser}" 设置新密码: `)
            if (!dbPassword) {
              colorLog('❌ 密码不能为空', 'red')
            }
          }

          try {
            await client.query(`ALTER USER ${dbUser} WITH PASSWORD '${dbPassword}'`)
            colorLog(`✅ 已更新用户 "${dbUser}" 的密码`, 'green')
          } catch (error) {
            colorLog(`⚠️  更新密码失败: ${error.message}`, 'yellow')
          }
        } else {
          // 如果不重置密码，则需要手动输入现有密码以生成连接字符串
          dbPassword = await askQuestion(`请输入用户 "${dbUser}" 的现有密码 (用于生成连接字符串): `)
          if (!dbPassword) {
            colorLog('⚠️  未提供密码，将使用空密码生成连接字符串', 'yellow')
            dbPassword = ''
          }
        }
      }

      await client.end()

      // 生成连接字符串
      const connectionString = `postgres://${dbUser}:${dbPassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${selectedDb}`

      // 更新环境变量
      try {
        const envPath = path.join(process.cwd(), '.env')
        let envContent = ''

        if (fs.existsSync(envPath)) {
          envContent = fs.readFileSync(envPath, 'utf8')

          // 检查是否已存在 DATABASE_URI
          if (envContent.includes('DATABASE_URI=')) {
            // 替换现有的 DATABASE_URI
            envContent = envContent.replace(/DATABASE_URI=.*/, `DATABASE_URI=${connectionString}`)
            colorLog('✅ 已更新环境变量 DATABASE_URI', 'green')
          } else {
            // 添加新的 DATABASE_URI
            envContent += `\nDATABASE_URI=${connectionString}\n`
            colorLog('✅ 已添加环境变量 DATABASE_URI', 'green')
          }
        } else {
          // 创建新的 .env 文件
          envContent = `DATABASE_URI=${connectionString}\n`
          colorLog('✅ 已创建 .env 文件并添加环境变量 DATABASE_URI', 'green')
        }

        fs.writeFileSync(envPath, envContent)

        colorLog('\n🔗 数据库连接字符串:', 'cyan')
        colorLog(`${connectionString}`, 'yellow')
      } catch (error) {
        colorLog(`❌ 更新环境变量失败: ${error.message}`, 'red')
      }
    } catch (error) {
      colorLog(`❌ 数据库操作失败: ${error.message}`, 'red')
    } finally {
      if (client.connected) {
        await client.end().catch(() => {})
      }
    }
  }
}

// 创建新数据库
async function createNewDatabase() {
  colorLog('\n🆕 创建新数据库', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 已连接到 PostgreSQL', 'green')

    // 获取新数据库名称
    let newDbName = ''
    while (!newDbName) {
      newDbName = await askQuestion('请输入新数据库名称: ')
      if (!newDbName) {
        colorLog('❌ 数据库名称不能为空', 'red')
        continue
      }

      if (!/^[a-z0-9_]+$/.test(newDbName)) {
        colorLog('❌ 数据库名称只能包含小写字母、数字和下划线', 'red')
        newDbName = ''
        continue
      }

      // 检查数据库是否已存在
      const dbExists = await client.query(`SELECT 1 FROM pg_database WHERE datname = $1`, [
        newDbName,
      ])
      if (dbExists.rows.length > 0) {
        colorLog(`❌ 数据库 "${newDbName}" 已存在`, 'red')
        newDbName = ''
        continue
      }
    }

    // 获取用户名（默认与数据库名相同）
    const defaultUser = newDbName
    const newDbUser =
      (await askQuestion(`请输入数据库用户名 (默认: ${defaultUser}): `)) || defaultUser

    // 生成随机密码
    const generateRandomPassword = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
      let password = ''
      for (let i = 0; i < 16; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return password
    }

    const defaultPassword = generateRandomPassword()
    colorLog(`🔑 已生成随机密码: ${defaultPassword}`, 'yellow')

    // 获取密码
    const newDbPassword =
      (await askQuestion(`请输入数据库密码 (默认使用生成的随机密码): `)) || defaultPassword

    // 确认创建
    colorLog('\n📋 即将创建以下数据库:', 'cyan')
    colorLog(`🗄️  数据库名称: ${newDbName}`, 'bright')
    colorLog(`👤 数据库用户: ${newDbUser}`, 'bright')
    colorLog(`🔑 数据库密码: ${'*'.repeat(newDbPassword.length)}`, 'bright')

    const confirmCreate = await askQuestion('\n确认创建? (y/N): ')
    if (confirmCreate.toLowerCase() !== 'y' && confirmCreate.toLowerCase() !== 'yes') {
      colorLog('❌ 操作已取消', 'yellow')
      await client.end()
      return
    }

    // 开始创建数据库
    colorLog('\n🚀 开始创建新数据库...', 'cyan')

    try {
      await client.query(`CREATE DATABASE ${newDbName}`)
      colorLog(`✅ 已创建数据库 "${newDbName}"`, 'green')
    } catch (error) {
      colorLog(`❌ 创建数据库失败: ${error.message}`, 'red')
      await client.end()
      return
    }

    // 创建用户
    try {
      await client.query(`CREATE USER ${newDbUser} WITH PASSWORD '${newDbPassword}'`)
      colorLog(`✅ 已创建用户 "${newDbUser}"`, 'green')
    } catch (error) {
      colorLog(`⚠️  创建用户失败: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }

    // 授予权限
    try {
      await client.query(`GRANT ALL PRIVILEGES ON DATABASE ${newDbName} TO ${newDbUser}`)
      colorLog(`✅ 已授予数据库权限给 "${newDbUser}"`, 'green')
    } catch (error) {
      colorLog(`⚠️  授权失败: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }

    await client.end()

    // 连接到新数据库设置 schema 权限
    try {
      const dbClient = new Client({
        host: DB_CONFIG.host,
        port: DB_CONFIG.port,
        user: DB_CONFIG.superUser,
        password: DB_CONFIG.superPassword,
        database: newDbName,
      })

      await dbClient.connect()
      await dbClient.query(`GRANT ALL ON SCHEMA public TO ${newDbUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${newDbUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${newDbUser}`)
      colorLog(`✅ 已设置 schema 权限`, 'green')
      await dbClient.end()
    } catch (error) {
      colorLog(`⚠️  设置 schema 权限失败: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }

    // 生成连接字符串
    const connectionString = `postgres://${newDbUser}:${newDbPassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${newDbName}`
    colorLog('\n🔗 数据库连接字符串:', 'cyan')
    colorLog(`${connectionString}`, 'yellow')

    // 询问是否更新环境变量
    const updateEnv = await askQuestion('\n是否更新/添加环境变量? (Y/n): ')
    if (updateEnv.toLowerCase() !== 'n' && updateEnv.toLowerCase() !== 'no') {
      try {
        const envPath = path.join(process.cwd(), '.env')
        let envContent = ''

        if (fs.existsSync(envPath)) {
          envContent = fs.readFileSync(envPath, 'utf8')

          // 检查是否已存在 DATABASE_URI
          if (envContent.includes('DATABASE_URI=')) {
            // 替换现有的 DATABASE_URI
            envContent = envContent.replace(/DATABASE_URI=.*/, `DATABASE_URI=${connectionString}`)
            colorLog('✅ 已更新环境变量 DATABASE_URI', 'green')
          } else {
            // 添加新的 DATABASE_URI
            envContent += `\nDATABASE_URI=${connectionString}\n`
            colorLog('✅ 已添加环境变量 DATABASE_URI', 'green')
          }
        } else {
          // 创建新的 .env 文件
          envContent = `DATABASE_URI=${connectionString}\n`
          colorLog('✅ 已创建 .env 文件并添加环境变量 DATABASE_URI', 'green')
        }

        fs.writeFileSync(envPath, envContent)
      } catch (error) {
        colorLog(`❌ 更新环境变量失败: ${error.message}`, 'red')
      }
    }

    colorLog('\n🎉 新数据库创建成功！', 'green')
  } catch (error) {
    colorLog(`❌ 创建数据库失败: ${error.message}`, 'red')
  } finally {
    if (client.connected) {
      await client.end().catch(() => {})
    }
  }
}

// 删除数据库
async function deleteDatabase() {
  colorLog('\n🗑️  删除数据库', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 已连接到 PostgreSQL', 'green')

    // 列出所有用户数据库
    const allDbResult = await client.query(`
      SELECT datname 
      FROM pg_database 
      WHERE datname NOT IN ('template0', 'template1', 'postgres')
      ORDER BY datname
    `)

    if (allDbResult.rows.length === 0) {
      colorLog('⚠️  未找到任何用户数据库', 'yellow')
      await client.end()
      return
    }

    colorLog('\n📋 选择要删除的数据库:', 'cyan')
    allDbResult.rows.forEach((row, index) => {
      colorLog(`${(index + 1).toString().padStart(2)}. ${row.datname}`, 'bright')
    })

    const dbChoice = await askQuestion('\n请选择数据库编号 (0 取消): ')
    if (dbChoice === '0') {
      colorLog('操作已取消', 'yellow')
      await client.end()
      return
    }

    const dbIndex = parseInt(dbChoice) - 1
    if (isNaN(dbIndex) || dbIndex < 0 || dbIndex >= allDbResult.rows.length) {
      colorLog('❌ 无效的选择', 'red')
      await client.end()
      return
    }

    const dbToDelete = allDbResult.rows[dbIndex].datname

    // 检查是否有同名用户
    const userResult = await client.query(`SELECT 1 FROM pg_user WHERE usename = $1`, [dbToDelete])
    const hasMatchingUser = userResult.rows.length > 0

    // 警告确认
    colorLog(`\n⚠️  警告：即将删除数据库 "${dbToDelete}"`, 'red')
    colorLog('⚠️  此操作将永久删除数据库中的所有数据，且无法恢复！', 'red')
    if (hasMatchingUser) {
      colorLog(`⚠️  同时将删除同名用户 "${dbToDelete}"`, 'red')
    }

    const confirm = await askQuestion('\n请输入数据库名称以确认删除: ')
    if (confirm !== dbToDelete) {
      colorLog('❌ 操作已取消：确认信息不匹配', 'yellow')
      await client.end()
      return
    }

    // 开始删除
    colorLog('\n🗑️  开始删除数据库...', 'cyan')

    // 断开所有连接
    try {
      await client.query(
        `
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = $1 AND pid <> pg_backend_pid()
      `,
        [dbToDelete],
      )
      colorLog('✅ 已断开所有现有连接', 'green')
    } catch (error) {
      colorLog(`⚠️  断开连接时出现问题: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }

    // 删除数据库
    try {
      await client.query(`DROP DATABASE ${dbToDelete}`)
      colorLog(`✅ 已删除数据库 "${dbToDelete}"`, 'green')
    } catch (error) {
      colorLog(`❌ 删除数据库失败: ${error.message}`, 'red')
      await client.end()
      return
    }

    // 删除用户（如果存在）
    if (hasMatchingUser) {
      try {
        await client.query(`DROP USER ${dbToDelete}`)
        colorLog(`✅ 已删除用户 "${dbToDelete}"`, 'green')
      } catch (error) {
        colorLog(`⚠️  删除用户失败: ${error.message}`, 'yellow')
        // 继续执行，不中断流程
      }
    }

    // 检查环境变量
    try {
      const envPath = path.join(process.cwd(), '.env')
      if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf8')

        // 检查是否包含被删除数据库的连接字符串
        if (envContent.includes(`/${dbToDelete}`)) {
          colorLog('\n⚠️  环境变量中可能包含已删除数据库的连接信息', 'yellow')
          const updateEnv = await askQuestion('是否从环境变量中移除该数据库的连接信息? (y/N): ')

          if (updateEnv.toLowerCase() === 'y' || updateEnv.toLowerCase() === 'yes') {
            // 简单替换，仅适用于标准格式的连接字符串
            const newEnvContent = envContent.replace(
              new RegExp(`DATABASE_URI=postgres://[^:]+:[^@]+@[^:]+:[^/]+/${dbToDelete}.*`),
              `DATABASE_URI=`,
            )

            fs.writeFileSync(envPath, newEnvContent)
            colorLog('✅ 已更新环境变量', 'green')
          }
        }
      }
    } catch (error) {
      colorLog(`⚠️  处理环境变量时出现问题: ${error.message}`, 'yellow')
    }

    colorLog('\n🎉 数据库删除成功！', 'green')
  } catch (error) {
    colorLog(`❌ 删除数据库失败: ${error.message}`, 'red')
  } finally {
    if (client.connected) {
      await client.end().catch(() => {})
    }
  }
}

// 主程序
async function main() {
  try {
    while (true) {
      showMenu()
      const choice = await askQuestion('请选择操作 (0-7): ')

      switch (choice) {
        case '1':
          await testConnection()
          break
        case '2':
          await initializeDatabase()
          break
        case '3':
          await rebuildDatabase()
          break
        case '4':
          await runMigrations()
          break
        case '5':
          await deleteDatabase()
          break
        case '6':
          await createNewDatabase()
          break
        case '7':
          await modifyDatabaseConfig()
          break
        case '0':
          colorLog('\n👋 再见！', 'green')
          rl.close()
          return
        default:
          colorLog('\n❌ 无效选择，请重新输入', 'red')
      }

      if (choice !== '0') {
        await askQuestion('\n按 Enter 键继续...')
      }
    }
  } catch (error) {
    colorLog(`\n💥 程序出现错误: ${error.message}`, 'red')
    rl.close()
    process.exit(1)
  }
}

// 启动程序
if (require.main === module) {
  main()
}
