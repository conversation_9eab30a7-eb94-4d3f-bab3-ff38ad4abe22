#!/usr/bin/env node

// 真实邮件发送测试脚本
const nodemailer = require('nodemailer')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

// 已验证可用的配置
const workingConfigs = [
  {
    name: 'QQ邮箱 STARTTLS',
    host: 'smtp.qq.com',
    port: 587,
    secure: false,
    tls: {
      rejectUnauthorized: false
    },
    authType: 'QQ邮箱授权码'
  },
  {
    name: 'QQ邮箱 SSL',
    host: 'smtp.qq.com',
    port: 465,
    secure: true,
    tls: {
      rejectUnauthorized: false
    },
    authType: 'QQ邮箱授权码'
  },
  {
    name: '163邮箱',
    host: 'smtp.163.com',
    port: 25,
    secure: false,
    tls: {
      rejectUnauthorized: false
    },
    authType: '163邮箱授权码'
  }
]

async function testRealEmail(config, credentials) {
  console.log(`\n🔄 测试真实邮件发送: ${config.name}`)
  console.log(`服务器: ${config.host}:${config.port}`)
  
  try {
    const transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: {
        user: credentials.username,
        pass: credentials.password
      },
      tls: config.tls
    })
    
    // 验证连接
    console.log('🔍 验证SMTP连接...')
    await transporter.verify()
    console.log('✅ SMTP连接验证成功!')
    
    // 发送测试邮件
    console.log('📤 发送测试邮件...')
    const info = await transporter.sendMail({
      from: `"CMS邮件测试" <${credentials.username}>`,
      to: credentials.testEmail,
      subject: `🎉 CMS邮件服务测试成功 - ${config.name} - ${new Date().toLocaleString()}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #28a745;">🎉 邮件配置测试成功！</h2>
          <p>恭喜！您的邮件配置工作正常，可以在CMS中使用。</p>
          
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #495057;">📋 成功的配置信息：</h3>
            <ul style="list-style: none; padding: 0;">
              <li><strong>服务:</strong> ${config.name}</li>
              <li><strong>SMTP服务器:</strong> ${config.host}</li>
              <li><strong>端口:</strong> ${config.port}</li>
              <li><strong>安全连接:</strong> ${config.secure ? 'SSL' : 'STARTTLS'}</li>
              <li><strong>发件人:</strong> ${credentials.username}</li>
              <li><strong>测试时间:</strong> ${new Date().toLocaleString()}</li>
            </ul>
          </div>
          
          <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #155724;">🎯 CMS配置指南：</h3>
            <p style="margin: 0; color: #155724;">
              请在CMS的"邮件服务配置"中使用以下设置：<br>
              <strong>SMTP服务器:</strong> ${config.host}<br>
              <strong>端口:</strong> ${config.port}<br>
              <strong>使用SSL/TLS:</strong> ${config.secure ? '勾选' : '不勾选'}<br>
              <strong>用户名:</strong> ${credentials.username}<br>
              <strong>密码:</strong> [您的${config.authType}]
            </p>
          </div>
          
          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 20px 0;">
          <p style="color: #6c757d; font-size: 0.9em;">
            这封邮件是由CMS邮件服务测试脚本自动发送的。
          </p>
        </div>
      `,
      text: `
CMS邮件服务测试成功！

成功的配置信息：
- 服务: ${config.name}
- SMTP服务器: ${config.host}
- 端口: ${config.port}
- 安全连接: ${config.secure ? 'SSL' : 'STARTTLS'}
- 发件人: ${credentials.username}
- 测试时间: ${new Date().toLocaleString()}

CMS配置指南：
请在CMS的"邮件服务配置"中使用以下设置：
SMTP服务器: ${config.host}
端口: ${config.port}
使用SSL/TLS: ${config.secure ? '勾选' : '不勾选'}
用户名: ${credentials.username}
密码: [您的${config.authType}]

这封邮件是由CMS邮件服务测试脚本自动发送的。
      `
    })
    
    console.log(`✅ 测试邮件发送成功!`)
    console.log(`📧 邮件已发送到: ${credentials.testEmail}`)
    console.log(`📨 消息ID: ${info.messageId}`)
    
    return {
      success: true,
      config: config,
      credentials: credentials,
      messageId: info.messageId
    }
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`)
    
    if (error.code === 'EAUTH') {
      console.log(`💡 认证失败 - 请检查${config.authType}是否正确`)
      if (config.host.includes('qq.com')) {
        console.log('   QQ邮箱授权码获取: https://service.mail.qq.com/detail/0/53')
      } else if (config.host.includes('163.com')) {
        console.log('   163邮箱授权码获取: 邮箱设置 → POP3/SMTP/IMAP → 开启服务')
      }
    }
    
    return {
      success: false,
      config: config,
      error: error.message,
      errorCode: error.code
    }
  }
}

async function main() {
  console.log('🧪 真实邮件发送测试工具')
  console.log('=' * 50)
  console.log('使用已验证可用的邮件配置进行真实邮件发送测试\n')
  
  // 显示可用配置
  console.log('📋 可用的邮件配置:')
  workingConfigs.forEach((config, index) => {
    console.log(`${index + 1}. ${config.name} (${config.host}:${config.port})`)
  })
  
  const choice = await question('\n请选择要测试的配置 (输入数字): ')
  const selectedConfig = workingConfigs[parseInt(choice) - 1]
  
  if (!selectedConfig) {
    console.log('❌ 无效选择')
    rl.close()
    return
  }
  
  console.log(`\n已选择: ${selectedConfig.name}`)
  console.log(`需要提供: ${selectedConfig.authType}`)
  
  // 获取认证信息
  const credentials = {}
  credentials.username = await question('\n请输入邮箱地址: ')
  credentials.password = await question(`请输入${selectedConfig.authType}: `)
  credentials.testEmail = await question('请输入测试邮件接收地址: ')
  
  // 执行测试
  const result = await testRealEmail(selectedConfig, credentials)
  
  console.log('\n' + '=' * 60)
  if (result.success) {
    console.log('🎉 真实邮件发送测试成功!')
    console.log('\n✅ 此配置可以在CMS中使用!')
    console.log('\n📋 CMS配置信息:')
    console.log(`SMTP服务器: ${result.config.host}`)
    console.log(`端口: ${result.config.port}`)
    console.log(`使用SSL/TLS: ${result.config.secure ? '勾选' : '不勾选'}`)
    console.log(`用户名: ${result.credentials.username}`)
    console.log(`密码: [您的${result.config.authType}]`)
  } else {
    console.log('❌ 真实邮件发送测试失败')
    console.log(`错误: ${result.error}`)
  }
  
  rl.close()
}

if (require.main === module) {
  main().catch(console.error)
}
