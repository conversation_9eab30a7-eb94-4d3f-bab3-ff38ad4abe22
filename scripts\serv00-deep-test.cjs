#!/usr/bin/env node

// Serv00 深度连接测试
const nodemailer = require('nodemailer')
const net = require('net')

console.log('🔍 Serv00 邮件服务器深度诊断')
console.log('=' * 50)

// 第一步：基础网络连接测试
async function testNetworkConnection(host, port) {
  return new Promise((resolve) => {
    console.log(`🌐 测试网络连接: ${host}:${port}`)
    
    const socket = net.createConnection(port, host)
    let connected = false
    
    socket.on('connect', () => {
      connected = true
      console.log(`✅ 网络连接成功: ${host}:${port}`)
      socket.destroy()
      resolve(true)
    })
    
    socket.on('error', (err) => {
      if (!connected) {
        console.log(`❌ 网络连接失败: ${host}:${port} - ${err.message}`)
        resolve(false)
      }
    })
    
    socket.setTimeout(10000, () => {
      if (!connected) {
        console.log(`⏰ 网络连接超时: ${host}:${port}`)
        socket.destroy()
        resolve(false)
      }
    })
  })
}

// 第二步：SMTP协议握手测试
async function testSMTPHandshake(host, port, secure = false) {
  return new Promise((resolve) => {
    console.log(`🤝 测试SMTP握手: ${host}:${port} (${secure ? 'SSL' : 'Plain'})`)
    
    try {
      const transporter = nodemailer.createTransport({
        host: host,
        port: port,
        secure: secure,
        tls: {
          rejectUnauthorized: false,
          ciphers: 'SSLv3'
        },
        connectionTimeout: 15000,
        greetingTimeout: 10000,
        socketTimeout: 10000
      })
      
      transporter.verify((error, success) => {
        if (error) {
          if (error.code === 'EAUTH') {
            console.log(`✅ SMTP握手成功: ${host}:${port} (需要认证)`)
            resolve(true)
          } else {
            console.log(`❌ SMTP握手失败: ${host}:${port} - ${error.message}`)
            resolve(false)
          }
        } else {
          console.log(`✅ SMTP握手成功: ${host}:${port}`)
          resolve(true)
        }
      })
      
    } catch (error) {
      console.log(`❌ SMTP握手异常: ${host}:${port} - ${error.message}`)
      resolve(false)
    }
  })
}

// 第三步：带认证的连接测试
async function testSMTPAuth(host, port, secure, username, password) {
  return new Promise((resolve) => {
    console.log(`🔐 测试SMTP认证: ${host}:${port}`)
    
    try {
      const transporter = nodemailer.createTransport({
        host: host,
        port: port,
        secure: secure,
        auth: {
          user: username,
          pass: password
        },
        tls: {
          rejectUnauthorized: false,
          ciphers: 'SSLv3'
        },
        connectionTimeout: 15000,
        greetingTimeout: 10000,
        socketTimeout: 10000
      })
      
      transporter.verify((error, success) => {
        if (error) {
          console.log(`❌ SMTP认证失败: ${error.message}`)
          if (error.code === 'EAUTH') {
            console.log(`💡 认证错误可能原因:`)
            console.log(`   - 用户名或密码错误`)
            console.log(`   - 账户被锁定或暂停`)
            console.log(`   - 需要在Serv00面板中重置密码`)
          }
          resolve(false)
        } else {
          console.log(`✅ SMTP认证成功!`)
          resolve(true)
        }
      })
      
    } catch (error) {
      console.log(`❌ SMTP认证异常: ${error.message}`)
      resolve(false)
    }
  })
}

// 第四步：实际邮件发送测试
async function testEmailSending(host, port, secure, username, password, testEmail) {
  return new Promise((resolve) => {
    console.log(`📧 测试邮件发送: ${testEmail}`)
    
    try {
      const transporter = nodemailer.createTransport({
        host: host,
        port: port,
        secure: secure,
        auth: {
          user: username,
          pass: password
        },
        tls: {
          rejectUnauthorized: false,
          ciphers: 'SSLv3'
        }
      })
      
      const mailOptions = {
        from: `"Serv00测试" <${username}>`,
        to: testEmail,
        subject: `Serv00邮件服务测试 - ${new Date().toLocaleString()}`,
        html: `
          <h2>🎉 Serv00邮件服务测试成功!</h2>
          <p>恭喜！您的Serv00邮件配置工作正常。</p>
          <hr>
          <p><strong>配置信息:</strong></p>
          <ul>
            <li>服务器: ${host}:${port}</li>
            <li>安全连接: ${secure ? 'SSL' : 'STARTTLS'}</li>
            <li>发件人: ${username}</li>
            <li>测试时间: ${new Date().toLocaleString()}</li>
          </ul>
          <p style="color: green;"><strong>✅ 此配置可以在CMS中使用!</strong></p>
        `,
        text: `
Serv00邮件服务测试成功!

配置信息:
- 服务器: ${host}:${port}
- 安全连接: ${secure ? 'SSL' : 'STARTTLS'}
- 发件人: ${username}
- 测试时间: ${new Date().toLocaleString()}

✅ 此配置可以在CMS中使用!
        `
      }
      
      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          console.log(`❌ 邮件发送失败: ${error.message}`)
          resolve(false)
        } else {
          console.log(`✅ 邮件发送成功! 消息ID: ${info.messageId}`)
          resolve(true)
        }
      })
      
    } catch (error) {
      console.log(`❌ 邮件发送异常: ${error.message}`)
      resolve(false)
    }
  })
}

async function main() {
  const host = 'mail9.serv00.com'
  const configs = [
    { port: 587, secure: false, name: 'STARTTLS' },
    { port: 465, secure: true, name: 'SSL' },
    { port: 25, secure: false, name: '非加密' }
  ]
  
  console.log('开始Serv00邮件服务器深度测试...\n')
  
  // 第一步：测试所有端口的网络连接
  console.log('🔍 第一步：网络连接测试')
  console.log('-' * 30)
  
  const networkResults = []
  for (const config of configs) {
    const result = await testNetworkConnection(host, config.port)
    networkResults.push({ ...config, networkOk: result })
  }
  
  // 第二步：测试SMTP握手
  console.log('\n🤝 第二步：SMTP协议握手测试')
  console.log('-' * 35)
  
  const smtpResults = []
  for (const config of networkResults) {
    if (config.networkOk) {
      const result = await testSMTPHandshake(host, config.port, config.secure)
      smtpResults.push({ ...config, smtpOk: result })
    } else {
      smtpResults.push({ ...config, smtpOk: false })
    }
  }
  
  // 显示中间结果
  console.log('\n📊 中间测试结果:')
  console.log('-' * 20)
  smtpResults.forEach(config => {
    const status = config.networkOk && config.smtpOk ? '✅ 可用' : '❌ 不可用'
    console.log(`${config.name} (${config.port}): ${status}`)
  })
  
  // 如果有可用的配置，进行认证测试
  const workingConfigs = smtpResults.filter(c => c.networkOk && c.smtpOk)
  
  if (workingConfigs.length > 0) {
    console.log('\n🔐 第三步：认证测试 (需要真实凭据)')
    console.log('-' * 40)
    console.log('请提供Serv00邮箱凭据进行完整测试:')
    console.log('用户名: <EMAIL>')
    console.log('密码: [您的Serv00邮箱密码]')
    console.log('测试邮箱: [接收测试邮件的地址]')
    console.log('')
    console.log('如果您有凭据，可以手动运行认证测试')
    
    // 这里可以添加交互式输入，但为了安全起见暂时注释
    /*
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    const username = await new Promise(resolve => rl.question('用户名: ', resolve))
    const password = await new Promise(resolve => rl.question('密码: ', resolve))
    const testEmail = await new Promise(resolve => rl.question('测试邮箱: ', resolve))
    
    for (const config of workingConfigs) {
      await testSMTPAuth(host, config.port, config.secure, username, password)
    }
    
    rl.close()
    */
  }
  
  // 最终总结
  console.log('\n' + '=' * 60)
  console.log('📋 Serv00邮件服务器测试总结')
  console.log('=' * 60)
  
  if (workingConfigs.length > 0) {
    console.log(`✅ 找到 ${workingConfigs.length} 个可用的Serv00配置:`)
    workingConfigs.forEach(config => {
      console.log(`   • ${config.name} (${host}:${config.port})`)
    })
    
    console.log('\n🎯 推荐配置:')
    const recommended = workingConfigs[0]
    console.log(`SMTP服务器: ${host}`)
    console.log(`端口: ${recommended.port}`)
    console.log(`使用SSL/TLS: ${recommended.secure ? '勾选' : '不勾选'}`)
    console.log(`用户名: <EMAIL>`)
    console.log(`密码: [您的Serv00邮箱密码]`)
    
    console.log('\n💡 下一步:')
    console.log('1. 确认Serv00邮箱密码正确')
    console.log('2. 在CMS中使用上述配置')
    console.log('3. 进行真实邮件发送测试')
    
  } else {
    console.log('❌ 所有Serv00配置都不可用')
    console.log('\n可能的原因:')
    console.log('1. Serv00 SMTP服务暂时关闭')
    console.log('2. 网络防火墙阻止连接')
    console.log('3. 服务器配置发生变更')
    
    console.log('\n建议:')
    console.log('1. 联系Serv00技术支持')
    console.log('2. 检查Serv00面板中的邮箱设置')
    console.log('3. 尝试从不同网络环境测试')
  }
}

main().catch(console.error)
