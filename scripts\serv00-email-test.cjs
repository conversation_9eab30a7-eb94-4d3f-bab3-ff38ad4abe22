#!/usr/bin/env node

// Serv00 邮件服务器专项测试
const nodemailer = require('nodemailer')

console.log('🧪 Serv00 邮件服务器专项测试')
console.log('=' * 50)

// Serv00 可能的配置组合
const serv00Configs = [
  {
    name: 'Serv00 STARTTLS (587)',
    host: 'mail9.serv00.com',
    port: 587,
    secure: false,
    description: '标准STARTTLS配置'
  },
  {
    name: 'Serv00 SSL (465)',
    host: 'mail9.serv00.com',
    port: 465,
    secure: true,
    description: 'SSL直连配置'
  },
  {
    name: 'Serv00 非加密 (25)',
    host: 'mail9.serv00.com',
    port: 25,
    secure: false,
    description: '非加密连接'
  },
  {
    name: 'Serv00 备用SSL (993)',
    host: 'mail9.serv00.com',
    port: 993,
    secure: true,
    description: 'IMAP SSL端口测试'
  },
  {
    name: 'Serv00 备用 (143)',
    host: 'mail9.serv00.com',
    port: 143,
    secure: false,
    description: 'IMAP非加密端口测试'
  }
]

// 不同的TLS配置
const tlsVariations = [
  {
    name: '标准TLS',
    tls: {
      rejectUnauthorized: false
    }
  },
  {
    name: '宽松TLS',
    tls: {
      rejectUnauthorized: false,
      ciphers: 'SSLv3'
    }
  },
  {
    name: '兼容TLS',
    tls: {
      rejectUnauthorized: false,
      secureProtocol: 'TLSv1_method'
    }
  },
  {
    name: '最宽松TLS',
    tls: {
      rejectUnauthorized: false,
      checkServerIdentity: () => undefined,
      secureProtocol: 'SSLv23_method'
    }
  },
  {
    name: '无TLS',
    tls: undefined
  }
]

async function testServ00Config(baseConfig, tlsConfig, withAuth = false) {
  try {
    const transportConfig = {
      host: baseConfig.host,
      port: baseConfig.port,
      secure: baseConfig.secure,
      ...tlsConfig
    }
    
    // 如果测试认证，使用示例认证信息
    if (withAuth) {
      transportConfig.auth = {
        user: '<EMAIL>', // 您之前使用的邮箱
        pass: 'test-password'         // 占位符密码
      }
    }
    
    const transporter = nodemailer.createTransport(transportConfig)
    
    // 验证连接
    await transporter.verify()
    return { success: true, error: null }
    
  } catch (error) {
    // 如果是认证错误，说明连接成功
    if (error.code === 'EAUTH' || error.message.includes('535') || error.message.includes('authentication')) {
      return { success: true, error: 'AUTH_REQUIRED', message: '连接成功，需要正确认证' }
    }
    return { success: false, error: error.code, message: error.message }
  }
}

async function main() {
  console.log('开始测试 Serv00 邮件服务器的各种配置...\n')
  
  const successfulConfigs = []
  
  for (const baseConfig of serv00Configs) {
    console.log(`\n📧 测试: ${baseConfig.name}`)
    console.log(`服务器: ${baseConfig.host}:${baseConfig.port}`)
    console.log(`描述: ${baseConfig.description}`)
    
    let foundWorking = false
    
    // 先测试无认证连接
    console.log('\n  🔍 测试无认证连接:')
    for (const tlsConfig of tlsVariations) {
      process.stdout.write(`    ${tlsConfig.name}... `)
      
      const result = await testServ00Config(baseConfig, tlsConfig, false)
      
      if (result.success) {
        console.log('✅ 成功!')
        successfulConfigs.push({
          ...baseConfig,
          ...tlsConfig,
          configName: `${baseConfig.name} (${tlsConfig.name})`,
          authRequired: result.error === 'AUTH_REQUIRED'
        })
        foundWorking = true
        break
      } else {
        console.log(`❌ ${result.error}`)
      }
    }
    
    // 如果无认证失败，测试带认证的连接
    if (!foundWorking) {
      console.log('\n  🔐 测试带认证连接:')
      for (const tlsConfig of tlsVariations) {
        process.stdout.write(`    ${tlsConfig.name}... `)
        
        const result = await testServ00Config(baseConfig, tlsConfig, true)
        
        if (result.success) {
          console.log(result.error === 'AUTH_REQUIRED' ? '✅ 连接成功(需认证)' : '✅ 成功!')
          successfulConfigs.push({
            ...baseConfig,
            ...tlsConfig,
            configName: `${baseConfig.name} (${tlsConfig.name})`,
            authRequired: true
          })
          foundWorking = true
          break
        } else {
          console.log(`❌ ${result.error}`)
        }
      }
    }
    
    if (!foundWorking) {
      console.log(`  ❌ ${baseConfig.name} - 所有配置都失败`)
    }
  }
  
  console.log('\n' + '='.repeat(70))
  console.log('📊 Serv00 测试结果总结')
  console.log('='.repeat(70))
  
  if (successfulConfigs.length === 0) {
    console.log('❌ Serv00 邮件服务器无法连接')
    console.log('\n可能的原因:')
    console.log('1. Serv00 SMTP服务暂时不可用')
    console.log('2. 服务器配置发生变更')
    console.log('3. 网络防火墙阻止连接')
    console.log('4. 需要特殊的认证方式')
    console.log('\n建议:')
    console.log('1. 联系 Serv00 技术支持确认SMTP设置')
    console.log('2. 检查账户状态是否正常')
    console.log('3. 尝试使用其他邮件服务 (QQ邮箱/163邮箱)')
  } else {
    console.log(`✅ 找到 ${successfulConfigs.length} 个可用的 Serv00 配置:\n`)
    
    successfulConfigs.forEach((config, index) => {
      console.log(`${index + 1}. ${config.configName}`)
      console.log(`   服务器: ${config.host}:${config.port}`)
      console.log(`   安全连接: ${config.secure ? 'SSL' : 'STARTTLS'}`)
      console.log(`   需要认证: ${config.authRequired ? '是' : '否'}`)
      if (config.tls) {
        console.log(`   TLS配置: ${JSON.stringify(config.tls)}`)
      }
      console.log('')
    })
    
    console.log('🎯 推荐的 Serv00 配置:')
    const recommended = successfulConfigs[0]
    console.log(`
SMTP服务器: ${recommended.host}
端口: ${recommended.port}
使用SSL/TLS: ${recommended.secure ? '勾选' : '不勾选'}
用户名: <EMAIL> (或您的Serv00邮箱)
密码: [您的Serv00邮箱密码]
${recommended.tls ? `TLS配置: ${JSON.stringify(recommended.tls)}` : ''}
    `)
    
    console.log('💡 提示:')
    console.log('- 确保使用正确的 Serv00 邮箱地址和密码')
    console.log('- 检查 Serv00 账户中的邮箱设置')
    console.log('- 如果仍然失败，建议使用 QQ邮箱或163邮箱作为替代方案')
  }
}

main().catch(console.error)
