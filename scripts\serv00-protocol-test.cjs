#!/usr/bin/env node

// Serv00 SMTP协议详细测试
const net = require('net')
const tls = require('tls')

console.log('🔍 Serv00 SMTP协议详细分析')
console.log('=' * 50)

// 手动SMTP协议测试
async function manualSMTPTest(host, port, useSSL = false) {
  return new Promise((resolve) => {
    console.log(`\n🔌 手动SMTP测试: ${host}:${port} (${useSSL ? 'SSL' : 'Plain'})`)
    
    let socket
    let data = ''
    
    const cleanup = () => {
      if (socket) {
        socket.destroy()
      }
    }
    
    const handleData = (chunk) => {
      data += chunk.toString()
      console.log(`📥 服务器响应: ${chunk.toString().trim()}`)
      
      // 检查SMTP欢迎消息
      if (data.includes('220')) {
        console.log('✅ 收到SMTP欢迎消息')
        
        // 发送EHLO命令
        console.log('📤 发送: EHLO test.com')
        socket.write('EHLO test.com\r\n')
      }
      
      // 检查EHLO响应
      if (data.includes('250') && data.includes('EHLO')) {
        console.log('✅ EHLO命令成功')
        
        // 发送QUIT命令
        console.log('📤 发送: QUIT')
        socket.write('QUIT\r\n')
      }
      
      // 检查QUIT响应
      if (data.includes('221')) {
        console.log('✅ SMTP会话正常结束')
        cleanup()
        resolve(true)
      }
    }
    
    const handleError = (error) => {
      console.log(`❌ 连接错误: ${error.message}`)
      cleanup()
      resolve(false)
    }
    
    const handleClose = () => {
      console.log('🔌 连接关闭')
      if (!data.includes('221')) {
        console.log('⚠️  连接意外关闭')
        resolve(false)
      }
    }
    
    try {
      if (useSSL) {
        // SSL连接
        socket = tls.connect({
          host: host,
          port: port,
          rejectUnauthorized: false
        })
      } else {
        // 普通连接
        socket = net.createConnection(port, host)
      }
      
      socket.on('data', handleData)
      socket.on('error', handleError)
      socket.on('close', handleClose)
      
      socket.setTimeout(15000, () => {
        console.log('⏰ 连接超时')
        cleanup()
        resolve(false)
      })
      
    } catch (error) {
      console.log(`❌ 连接异常: ${error.message}`)
      resolve(false)
    }
  })
}

// 测试不同的SMTP服务器
async function testAlternativeServers() {
  console.log('\n🔍 测试其他可能的Serv00邮件服务器')
  console.log('-' * 40)
  
  const alternativeServers = [
    'mail.serv00.com',
    'smtp.serv00.com',
    'mail.copilot.us.kg',
    'smtp.copilot.us.kg'
  ]
  
  for (const server of alternativeServers) {
    console.log(`\n🌐 测试服务器: ${server}`)
    
    // 测试网络连接
    const networkTest = await new Promise((resolve) => {
      const socket = net.createConnection(587, server)
      socket.on('connect', () => {
        console.log(`✅ ${server}:587 网络连接成功`)
        socket.destroy()
        resolve(true)
      })
      socket.on('error', () => {
        console.log(`❌ ${server}:587 网络连接失败`)
        resolve(false)
      })
      socket.setTimeout(5000, () => {
        socket.destroy()
        resolve(false)
      })
    })
    
    if (networkTest) {
      // 如果网络连接成功，测试SMTP
      await manualSMTPTest(server, 587, false)
    }
  }
}

// 检查DNS解析
async function checkDNSResolution() {
  console.log('\n🔍 DNS解析检查')
  console.log('-' * 20)
  
  const dns = require('dns')
  
  return new Promise((resolve) => {
    dns.lookup('mail9.serv00.com', (err, address, family) => {
      if (err) {
        console.log(`❌ DNS解析失败: ${err.message}`)
        resolve(false)
      } else {
        console.log(`✅ DNS解析成功: mail9.serv00.com -> ${address}`)
        
        // 检查MX记录
        dns.resolveMx('copilot.us.kg', (err, addresses) => {
          if (err) {
            console.log(`⚠️  MX记录查询失败: ${err.message}`)
          } else {
            console.log(`📧 MX记录:`)
            addresses.forEach(mx => {
              console.log(`   ${mx.priority} ${mx.exchange}`)
            })
          }
          resolve(true)
        })
      }
    })
  })
}

async function main() {
  console.log('开始Serv00 SMTP协议详细分析...\n')
  
  // 1. DNS检查
  await checkDNSResolution()
  
  // 2. 手动SMTP协议测试
  console.log('\n🔍 手动SMTP协议测试')
  console.log('-' * 25)
  
  const configs = [
    { port: 587, ssl: false, name: 'STARTTLS' },
    { port: 465, ssl: true, name: 'SSL' },
    { port: 25, ssl: false, name: '非加密' }
  ]
  
  const results = []
  for (const config of configs) {
    const result = await manualSMTPTest('mail9.serv00.com', config.port, config.ssl)
    results.push({ ...config, success: result })
  }
  
  // 3. 测试其他可能的服务器
  await testAlternativeServers()
  
  // 4. 总结和建议
  console.log('\n' + '=' * 60)
  console.log('📋 详细分析结果')
  console.log('=' * 60)
  
  const workingConfigs = results.filter(r => r.success)
  
  if (workingConfigs.length > 0) {
    console.log('✅ 找到可用的SMTP配置:')
    workingConfigs.forEach(config => {
      console.log(`   • ${config.name} (mail9.serv00.com:${config.port})`)
    })
  } else {
    console.log('❌ 所有配置都失败')
    
    console.log('\n🔍 可能的问题:')
    console.log('1. Serv00 SMTP服务需要特定的客户端标识')
    console.log('2. 可能需要从Serv00服务器内部连接')
    console.log('3. 可能需要特殊的认证序列')
    console.log('4. 服务器可能有IP白名单限制')
    
    console.log('\n💡 建议解决方案:')
    console.log('1. 检查Serv00面板中的邮箱设置')
    console.log('2. 确认邮箱账户状态正常')
    console.log('3. 尝试从Serv00服务器内部测试')
    console.log('4. 联系Serv00技术支持获取正确的SMTP配置')
    console.log('5. 考虑使用Serv00提供的其他邮件发送方式')
    
    console.log('\n🔄 临时解决方案:')
    console.log('如果Serv00 SMTP暂时不可用，可以考虑:')
    console.log('1. 使用QQ邮箱作为临时发送服务')
    console.log('2. 配置域名邮箱转发到QQ邮箱')
    console.log('3. 使用第三方邮件服务API')
  }
}

main().catch(console.error)
