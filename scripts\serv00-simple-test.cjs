#!/usr/bin/env node

// Serv00 简单测试
const nodemailer = require('nodemailer')

console.log('🧪 Serv00 邮件服务器简单测试')

const serv00Configs = [
  {
    name: 'Serv00 STARTTLS',
    host: 'mail9.serv00.com',
    port: 587,
    secure: false
  },
  {
    name: 'Serv00 SSL',
    host: 'mail9.serv00.com',
    port: 465,
    secure: true
  },
  {
    name: 'Serv00 非加密',
    host: 'mail9.serv00.com',
    port: 25,
    secure: false
  }
]

async function testServ00(config) {
  console.log(`\n🔄 测试: ${config.name}`)
  console.log(`服务器: ${config.host}:${config.port}`)
  
  try {
    const transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      tls: {
        rejectUnauthorized: false
      }
      // 不使用认证，只测试连接
    })
    
    await transporter.verify()
    console.log(`✅ ${config.name} - 连接成功!`)
    return true
    
  } catch (error) {
    if (error.code === 'EAUTH' || error.message.includes('535') || error.message.includes('authentication')) {
      console.log(`✅ ${config.name} - 连接成功，需要认证`)
      return true
    }
    console.log(`❌ ${config.name} - 失败: ${error.message}`)
    return false
  }
}

async function main() {
  console.log('开始测试 Serv00 配置...\n')
  
  let successCount = 0
  
  for (const config of serv00Configs) {
    const success = await testServ00(config)
    if (success) successCount++
  }
  
  console.log('\n' + '='.repeat(50))
  console.log(`📊 Serv00 测试结果: ${successCount}/${serv00Configs.length} 个配置可用`)
  
  if (successCount > 0) {
    console.log('\n✅ Serv00 邮件服务器可以连接!')
    console.log('推荐配置:')
    console.log('SMTP服务器: mail9.serv00.com')
    console.log('端口: 587')
    console.log('使用SSL/TLS: 不勾选')
    console.log('用户名: <EMAIL>')
    console.log('密码: [您的Serv00密码]')
  } else {
    console.log('\n❌ Serv00 邮件服务器无法连接')
    console.log('建议使用 QQ邮箱或163邮箱作为替代方案')
  }
}

main().catch(console.error)
