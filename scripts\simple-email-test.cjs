#!/usr/bin/env node

// 简单邮件测试脚本
const nodemailer = require('nodemailer')

console.log('🧪 简单邮件测试开始...')

// 测试配置列表
const testConfigs = [
  {
    name: 'Gmail STARTTLS',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>', // 请替换为真实邮箱
      pass: 'test-password', // 请替换为应用专用密码
    },
  },
  {
    name: 'Gmail SSL',
    host: 'smtp.gmail.com',
    port: 465,
    secure: true,
    auth: {
      user: '<EMAIL>', // 请替换为真实邮箱
      pass: 'test-password', // 请替换为应用专用密码
    },
  },
  {
    name: 'QQ邮箱 STARTTLS',
    host: 'smtp.qq.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>', // 请替换为真实QQ邮箱
      pass: 'test-auth-code', // 请替换为授权码
    },
  },
]

async function testConfig(config) {
  console.log(`\n🔄 测试: ${config.name}`)
  console.log(`服务器: ${config.host}:${config.port}`)

  try {
    const transporter = nodemailer.createTransport({
      ...config,
      tls: {
        rejectUnauthorized: false,
      },
    })

    // 只验证连接，不发送邮件
    await transporter.verify()
    console.log(`✅ ${config.name} - 连接成功!`)
    return true
  } catch (error) {
    console.log(`❌ ${config.name} - 失败: ${error.message}`)
    return false
  }
}

async function main() {
  console.log('开始测试各种邮件配置...\n')

  let successCount = 0

  for (const config of testConfigs) {
    const success = await testConfig(config)
    if (success) successCount++
  }

  console.log('\n' + '='.repeat(50))
  console.log(`📊 测试完成: ${successCount}/${testConfigs.length} 个配置可用`)

  if (successCount === 0) {
    console.log('\n❌ 所有配置都失败了!')
    console.log('可能的原因:')
    console.log('1. 网络连接问题')
    console.log('2. 防火墙阻止')
    console.log('3. 认证信息错误')
    console.log('4. 邮件服务器问题')
  } else {
    console.log('\n✅ 找到可用的配置!')
    console.log('请使用成功的配置在CMS中设置邮件服务')
  }
}

main().catch(console.error)
