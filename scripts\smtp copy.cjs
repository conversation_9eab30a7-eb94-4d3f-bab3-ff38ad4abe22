#!/usr/bin/env node

// SMTP邮件服务检测脚本
// 用法: node smtp-test.cjs

const nodemailer = require('nodemailer')
const readline = require('readline')
const fs = require('fs')
const path = require('path')

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function colorLog(message, color = 'reset') {
  console.log(colors[color] + message + colors.reset)
}

// 询问用户输入
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// SMTP配置变量
const SMTP_CONFIG = {
  host: 'mail9.serv00.com',
  port: 587, // 默认SMTP端口
  secure: false, // true为465端口，false为其他端口
  auth: {
    user: '<EMAIL>',
    pass: 'Sty94025@'
  },
  testRecipient: '<EMAIL>',
  tls: {
    rejectUnauthorized: false // 允许自签名证书
  }
}

// 配置文件路径
const CONFIG_FILE_PATH = path.join(__dirname, '.smtp-config.json')

// 读取配置文件
function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE_PATH)) {
      const configData = JSON.parse(fs.readFileSync(CONFIG_FILE_PATH, 'utf8'))
      
      // 更新配置
      if (configData.host) SMTP_CONFIG.host = configData.host
      if (configData.port) SMTP_CONFIG.port = parseInt(configData.port)
      if (configData.secure !== undefined) SMTP_CONFIG.secure = configData.secure
      if (configData.auth) {
        if (configData.auth.user) SMTP_CONFIG.auth.user = configData.auth.user
        if (configData.auth.pass) SMTP_CONFIG.auth.pass = configData.auth.pass
      }
      if (configData.testRecipient) SMTP_CONFIG.testRecipient = configData.testRecipient
      
      colorLog('✅ 已从配置文件加载设置', 'green')
      return true
    }
  } catch (error) {
    colorLog(`⚠️  读取配置文件失败: ${error.message}`, 'yellow')
  }
  return false
}

// 保存配置到文件
function saveConfig() {
  try {
    // 创建一个不包含敏感密码的配置副本
    const configToSave = { ...SMTP_CONFIG }
    
    // 询问是否保存密码
    const saveSensitiveData = process.env.SMTP_SAVE_PASSWORDS === 'true'
    
    // 如果不保存敏感数据，则清除密码
    if (!saveSensitiveData) {
      delete configToSave.auth.pass
    }
    
    fs.writeFileSync(CONFIG_FILE_PATH, JSON.stringify(configToSave, null, 2), 'utf8')
    colorLog('✅ 配置已保存到文件', 'green')
    return true
  } catch (error) {
    colorLog(`⚠️  保存配置文件失败: ${error.message}`, 'yellow')
    return false
  }
}

// 显示菜单
function showMenu() {
  console.clear()
  colorLog('╔══════════════════════════════════════════════════════╗', 'cyan')
  colorLog('║                SMTP邮件服务检测工具                  ║', 'cyan')
  colorLog('╠══════════════════════════════════════════════════════╣', 'cyan')
  colorLog('║                                                      ║', 'cyan')
  colorLog('║  1. 🔍 测试SMTP连接                                  ║', 'cyan')
  colorLog('║  2. 📧 发送测试邮件                                  ║', 'cyan')
  colorLog('║  3. ⚙️  修改SMTP配置                                 ║', 'cyan')
  colorLog('║  4. 📝 查看当前配置                                  ║', 'cyan')
  colorLog('║  0. 🚪 退出                                          ║', 'cyan')
  colorLog('║                                                      ║', 'cyan')
  colorLog('╚══════════════════════════════════════════════════════╝', 'cyan')
  console.log()
}

// 测试SMTP连接
async function testSMTPConnection() {
  colorLog('\n🔍 测试SMTP连接', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')
  
  colorLog(`\n📡 连接信息:`, 'cyan')
  colorLog(`🏠 服务器: ${SMTP_CONFIG.host}`, 'bright')
  colorLog(`🔌 端口: ${SMTP_CONFIG.port}`, 'bright')
  colorLog(`🔒 安全连接: ${SMTP_CONFIG.secure ? '是' : '否'}`, 'bright')
  colorLog(`👤 用户名: ${SMTP_CONFIG.auth.user}`, 'bright')
  
  try {
    colorLog('\n🔄 正在创建SMTP传输...', 'cyan')
    
    // 创建传输对象
    const transporter = nodemailer.createTransport({
      host: SMTP_CONFIG.host,
      port: SMTP_CONFIG.port,
      secure: SMTP_CONFIG.secure,
      auth: {
        user: SMTP_CONFIG.auth.user,
        pass: SMTP_CONFIG.auth.pass,
      },
      tls: SMTP_CONFIG.tls,
      debug: true,
    })
    
    colorLog('🔄 正在验证连接...', 'cyan')
    
    // 验证连接配置
    await transporter.verify()
    
    colorLog('✅ SMTP连接成功！服务器准备接受消息', 'green')
    return true
  } catch (error) {
    colorLog(`❌ SMTP连接失败: ${error.message}`, 'red')
    
    // 提供可能的解决方案
    colorLog('\n💡 可能的问题:', 'cyan')
    colorLog('1. 服务器地址错误', 'bright')
    colorLog('2. 端口号错误', 'bright')
    colorLog('3. 用户名或密码错误', 'bright')
    colorLog('4. 服务器要求SSL/TLS但未启用', 'bright')
    colorLog('5. 防火墙阻止连接', 'bright')
    
    return false
  }
}

// 发送测试邮件
async function sendTestEmail() {
  colorLog('\n📧 发送测试邮件', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')
  
  // 先测试连接
  const connectionSuccess = await testSMTPConnection()
  if (!connectionSuccess) {
    colorLog('⚠️  由于连接测试失败，无法发送测试邮件', 'yellow')
    return false
  }
  
  // 获取收件人邮箱
  const recipient = await askQuestion(`请输入收件人邮箱 (默认: ${SMTP_CONFIG.testRecipient}): `) || SMTP_CONFIG.testRecipient
  
  // 获取邮件主题
  const subject = await askQuestion('请输入邮件主题 (默认: SMTP测试邮件): ') || 'SMTP测试邮件'
  
  // 获取邮件内容
  const text = await askQuestion('请输入邮件内容 (默认: 这是一封测试邮件，用于验证SMTP服务是否正常工作。): ') 
    || '这是一封测试邮件，用于验证SMTP服务是否正常工作。'
  
  try {
    colorLog('\n🔄 正在创建SMTP传输...', 'cyan')
    
    // 创建传输对象
    const transporter = nodemailer.createTransport({
      host: SMTP_CONFIG.host,
      port: SMTP_CONFIG.port,
      secure: SMTP_CONFIG.secure,
      auth: {
        user: SMTP_CONFIG.auth.user,
        pass: SMTP_CONFIG.auth.pass,
      },
      tls: SMTP_CONFIG.tls,
    })
    
    colorLog(`🔄 正在发送邮件到 ${recipient}...`, 'cyan')
    
    // 发送邮件
    const info = await transporter.sendMail({
      from: `"SMTP测试" <${SMTP_CONFIG.auth.user}>`,
      to: recipient,
      subject: subject,
      text: text,
      html: `<p>${text}</p><p>发送时间: ${new Date().toLocaleString()}</p>`,
    })
    
    colorLog(`✅ 邮件发送成功！消息ID: ${info.messageId}`, 'green')
    
    if (info.accepted && info.accepted.length > 0) {
      colorLog(`✅ 接收者: ${info.accepted.join(', ')}`, 'green')
    }
    
    if (info.rejected && info.rejected.length > 0) {
      colorLog(`⚠️  被拒绝的接收者: ${info.rejected.join(', ')}`, 'yellow')
    }
    
    // 更新测试收件人
    SMTP_CONFIG.testRecipient = recipient
    saveConfig()
    
    return true
  } catch (error) {
    colorLog(`❌ 邮件发送失败: ${error.message}`, 'red')
    return false
  }
}

// 修改SMTP配置
async function modifySMTPConfig() {
  colorLog('\n⚙️  修改SMTP配置', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')
  
  colorLog('\n📡 当前SMTP配置:', 'cyan')
  colorLog(`🏠 服务器: ${SMTP_CONFIG.host}`, 'bright')
  colorLog(`🔌 端口: ${SMTP_CONFIG.port}`, 'bright')
  colorLog(`🔒 安全连接: ${SMTP_CONFIG.secure ? '是' : '否'}`, 'bright')
  colorLog(`👤 用户名: ${SMTP_CONFIG.auth.user}`, 'bright')
  colorLog(`📧 测试收件人: ${SMTP_CONFIG.testRecipient}`, 'bright')
  
  // 修改服务器
  const host = await askQuestion(`服务器地址 (默认: ${SMTP_CONFIG.host}): `)
  if (host) SMTP_CONFIG.host = host
  
  // 修改端口
  const port = await askQuestion(`端口 (默认: ${SMTP_CONFIG.port}): `)
  if (port) SMTP_CONFIG.port = parseInt(port)
  
  // 修改安全连接设置
  const secureInput = await askQuestion(`安全连接 (y/n) (默认: ${SMTP_CONFIG.secure ? 'y' : 'n'}): `)
  if (secureInput) {
    SMTP_CONFIG.secure = secureInput.toLowerCase() === 'y' || secureInput.toLowerCase() === 'yes'
    
    // 如果启用安全连接，自动设置端口为465
    if (SMTP_CONFIG.secure && SMTP_CONFIG.port !== 465) {
      const changePort = await askQuestion('安全连接通常使用465端口，是否自动修改端口为465? (Y/n): ')
      if (!changePort || changePort.toLowerCase() !== 'n') {
        SMTP_CONFIG.port = 465
        colorLog('✅ 已将端口修改为465', 'green')
      }
    }
  }
  
  // 修改用户名
  const user = await askQuestion(`用户名 (默认: ${SMTP_CONFIG.auth.user}): `)
  if (user) SMTP_CONFIG.auth.user = user
  
  // 修改密码
  const pass = await askQuestion('密码 (留空则保持不变): ')
  if (pass) SMTP_CONFIG.auth.pass = pass
  
  // 修改测试收件人
  const testRecipient = await askQuestion(`测试收件人 (默认: ${SMTP_CONFIG.testRecipient}): `)
  if (testRecipient) SMTP_CONFIG.testRecipient = testRecipient
  
  // 保存配置
  saveConfig()
  
  colorLog('\n✅ SMTP配置已更新', 'green')
}

// 查看当前配置
function viewCurrentConfig() {
  colorLog('\n📝 当前SMTP配置', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')
  
  colorLog('\n📡 SMTP服务器信息:', 'cyan')
  colorLog(`🏠 服务器: ${SMTP_CONFIG.host}`, 'bright')
  colorLog(`🔌 端口: ${SMTP_CONFIG.port}`, 'bright')
  colorLog(`🔒 安全连接: ${SMTP_CONFIG.secure ? '是' : '否'}`, 'bright')
  
  colorLog('\n👤 认证信息:', 'cyan')
  colorLog(`📧 用户名: ${SMTP_CONFIG.auth.user}`, 'bright')
  colorLog(`🔑 密码: ${'*'.repeat(SMTP_CONFIG.auth.pass.length)}`, 'bright')
  
  colorLog('\n📧 测试设置:', 'cyan')
  colorLog(`📩 测试收件人: ${SMTP_CONFIG.testRecipient}`, 'bright')
  
  colorLog('\n💡 提示:', 'cyan')
  colorLog(`常用SMTP端口: 25 (非加密), 465 (SSL/TLS), 587 (STARTTLS)`, 'bright')
  
  // 生成环境变量格式
  colorLog('\n🔧 环境变量格式:', 'cyan')
  colorLog(`SMTP_HOST=${SMTP_CONFIG.host}`, 'yellow')
  colorLog(`SMTP_PORT=${SMTP_CONFIG.port}`, 'yellow')
  colorLog(`SMTP_SECURE=${SMTP_CONFIG.secure}`, 'yellow')
  colorLog(`SMTP_USER=${SMTP_CONFIG.auth.user}`, 'yellow')
  colorLog(`SMTP_PASS=${SMTP_CONFIG.auth.pass}`, 'yellow')
  
  // 生成Payload配置格式
  colorLog('\n🔧 Payload配置格式:', 'cyan')
  colorLog(`email: {`, 'yellow')
  colorLog(`  transportOptions: {`, 'yellow')
  colorLog(`    host: '${SMTP_CONFIG.host}',`, 'yellow')
  colorLog(`    port: ${SMTP_CONFIG.port},`, 'yellow')
  colorLog(`    secure: ${SMTP_CONFIG.secure},`, 'yellow')
  colorLog(`    auth: {`, 'yellow')
  colorLog(`      user: '${SMTP_CONFIG.auth.user}',`, 'yellow')
  colorLog(`      pass: '${SMTP_CONFIG.auth.pass}'`, 'yellow')
  colorLog(`    }`, 'yellow')
  colorLog(`  },`, 'yellow')
  colorLog(`  fromName: 'Your Site Name',`, 'yellow')
  colorLog(`  fromAddress: '${SMTP_CONFIG.auth.user}'`, 'yellow')
  colorLog(`}`, 'yellow')
}

// 主程序
async function main() {
  try {
    // 检查是否安装了nodemailer
    try {
      require.resolve('nodemailer')
    } catch (e) {
      colorLog('⚠️  未检测到nodemailer包，正在安装...', 'yellow')
      const { execSync } = require('child_process')
      
      // 检查包管理器
      const packageManager = fs.existsSync('pnpm-lock.yaml') ? 'pnpm' : 
                           fs.existsSync('yarn.lock') ? 'yarn' : 'npm'
      
      try {
        execSync(`${packageManager} ${packageManager === 'yarn' ? 'add' : 'install'} nodemailer`, {
          stdio: 'inherit',
          cwd: process.cwd()
        })
        colorLog('✅ nodemailer安装成功', 'green')
      } catch (error) {
        colorLog(`❌ 安装nodemailer失败: ${error.message}`, 'red')
        colorLog('💡 请手动运行: npm install nodemailer', 'bright')
        process.exit(1)
      }
    }
    
    // 加载配置
    loadConfig()
    
    while (true) {
      showMenu()
      const choice = await askQuestion('请选择操作 (0-4): ')

      switch (choice) {
        case '1':
          await testSMTPConnection()
          break
        case '2':
          await sendTestEmail()
          break
        case '3':
          await modifySMTPConfig()
          break
        case '4':
          viewCurrentConfig()
          break
        case '0':
          colorLog('\n👋 再见！', 'green')
          rl.close()
          return
        default:
          colorLog('\n❌ 无效选择，请重新输入', 'red')
      }

      if (choice !== '0') {
        await askQuestion('\n按 Enter 键继续...')
      }
    }
  } catch (error) {
    colorLog(`\n💥 程序出现错误: ${error.message}`, 'red')
    rl.close()
    process.exit(1)
  }
}

// 启动程序
if (require.main === module) {
  main()
} 