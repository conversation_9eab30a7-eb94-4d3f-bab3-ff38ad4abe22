import type { Block, Field } from 'payload'

import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

import { link } from '@/fields/link'

const columnFields: Field[] = [
  {
    name: 'size',
    type: 'select',
    label: {
      en: 'Column Size',
      zh: '列宽度',
    },
    defaultValue: 'oneThird',
    options: [
      {
        label: {
          en: 'One Third',
          zh: '三分之一',
        },
        value: 'oneThird',
      },
      {
        label: {
          en: 'Half',
          zh: '一半',
        },
        value: 'half',
      },
      {
        label: {
          en: 'Two Thirds',
          zh: '三分之二',
        },
        value: 'twoThirds',
      },
      {
        label: {
          en: 'Full',
          zh: '全宽',
        },
        value: 'full',
      },
    ],
  },
  {
    name: 'richText',
    type: 'richText',
    editor: lexicalEditor({
      features: ({ rootFeatures }) => {
        return [
          ...rootFeatures,
          HeadingFeature({ enabledHeadingSizes: ['h2', 'h3', 'h4'] }),
          FixedToolbarFeature(),
          InlineToolbarFeature(),
        ]
      },
    }),
    label: false,
  },
  {
    name: 'enableLink',
    type: 'checkbox',
    label: {
      en: 'Enable Link',
      zh: '启用链接',
    },
  },
  link({
    overrides: {
      admin: {
        condition: (_data, siblingData) => {
          return Boolean(siblingData?.enableLink)
        },
      },
    },
  }),
]

export const Content: Block = {
  slug: 'content',
  interfaceName: 'ContentBlock',
  fields: [
    {
      name: 'columns',
      type: 'array',
      admin: {
        initCollapsed: true,
      },
      fields: columnFields,
    },
  ],
}
