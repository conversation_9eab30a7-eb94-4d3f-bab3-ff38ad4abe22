import type { GlobalAfterChangeHook } from 'payload'

export const ensureOneActiveEmailConfig: GlobalAfterChangeHook = async ({
  doc,
  req,
  previousDoc,
}) => {
  const { payload } = req

  // 如果没有邮箱配置，直接返回
  if (!doc.emailConfigs || doc.emailConfigs.length === 0) {
    return doc
  }

  // 检查是否有多个活跃配置
  const activeConfigs = doc.emailConfigs.filter((config: any) => config.isActive)

  if (activeConfigs.length > 1) {
    // 如果有多个活跃配置，只保留最后一个设置为活跃的
    const updatedConfigs = doc.emailConfigs.map((config: any, index: number) => {
      // 找到最后一个被设置为活跃的配置
      const lastActiveIndex = doc.emailConfigs
        .map((c: any, i: number) => ({ config: c, index: i }))
        .filter(({ config }: any) => config.isActive)
        .pop()?.index

      return {
        ...config,
        isActive: index === lastActiveIndex,
      }
    })

    // 更新文档
    const updatedDoc = await payload.updateGlobal({
      slug: 'emailService',
      data: {
        ...doc,
        emailConfigs: updatedConfigs,
      },
    })

    payload.logger.info('Email service configuration updated: ensured only one active config')
    return updatedDoc
  }

  // 如果没有活跃配置，将第一个设置为活跃
  if (activeConfigs.length === 0 && doc.emailConfigs.length > 0) {
    const updatedConfigs = doc.emailConfigs.map((config: any, index: number) => ({
      ...config,
      isActive: index === 0,
    }))

    const updatedDoc = await payload.updateGlobal({
      slug: 'emailService',
      data: {
        ...doc,
        emailConfigs: updatedConfigs,
      },
    })

    payload.logger.info('Email service configuration updated: set first config as active')
    return updatedDoc
  }

  return doc
}
