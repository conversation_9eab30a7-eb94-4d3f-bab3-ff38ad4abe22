import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { formBuilderPlugin } from '@payloadcms/plugin-form-builder'
import { nestedDocsPlugin } from '@payloadcms/plugin-nested-docs'
import { redirectsPlugin } from '@payloadcms/plugin-redirects'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { searchPlugin } from '@payloadcms/plugin-search'
import { Plugin } from 'payload'
import { revalidateRedirects } from '@/hooks/revalidateRedirects'
import { GenerateTitle, GenerateURL } from '@payloadcms/plugin-seo/types'
import { FixedToolbarFeature, HeadingFeature, lexicalEditor } from '@payloadcms/richtext-lexical'
import { searchFields } from '@/search/fieldOverrides'
import { beforeSyncWithSearch } from '@/search/beforeSync'

import { Page, Post } from '@/payload-types'
import { getServerSideURL } from '@/utilities/getURL'

const generateTitle: GenerateTitle<Post | Page> = ({ doc }) => {
  return doc?.title ? `${doc.title} | Payload Website Template` : 'Payload Website Template'
}

const generateURL: GenerateURL<Post | Page> = ({ doc }) => {
  const url = getServerSideURL()

  return doc?.slug ? `${url}/${doc.slug}` : url
}

export const plugins: Plugin[] = [
  redirectsPlugin({
    collections: ['pages', 'posts'],
    overrides: {
      labels: {
        singular: {
          en: 'Redirect',
          zh: '重定向',
        },
        plural: {
          en: 'Redirects',
          zh: '重定向',
        },
      },
      // @ts-expect-error - This is a valid override, mapped fields don't resolve to the same type
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'from') {
            return {
              ...field,
              label: {
                en: 'From',
                zh: '来源',
              },
              admin: {
                description: {
                  en: 'You will need to rebuild the website when changing this field.',
                  zh: '更改此字段时需要重新构建网站。',
                },
              },
            }
          }
          if ('name' in field && field.name === 'to') {
            return {
              ...field,
              label: {
                en: 'To',
                zh: '目标',
              },
              ...('fields' in field && field.fields
                ? {
                    fields: field.fields.map((subField: any) => {
                      if ('name' in subField && subField.name === 'type') {
                        return {
                          ...subField,
                          label: {
                            en: 'To URL Type',
                            zh: '目标网址类型',
                          },
                          options: subField.options?.map((option: any) => {
                            if (option.value === 'reference') {
                              return {
                                ...option,
                                label: {
                                  en: 'Internal link',
                                  zh: '内部链接',
                                },
                              }
                            }
                            if (option.value === 'custom') {
                              return {
                                ...option,
                                label: {
                                  en: 'Custom URL',
                                  zh: '自定义网址',
                                },
                              }
                            }
                            return option
                          }),
                        }
                      }
                      if ('name' in subField && subField.name === 'reference') {
                        return {
                          ...subField,
                          label: {
                            en: 'Document to redirect to',
                            zh: '重定向到的文档',
                          },
                        }
                      }
                      if ('name' in subField && subField.name === 'url') {
                        return {
                          ...subField,
                          label: {
                            en: 'Custom URL',
                            zh: '自定义网址',
                          },
                        }
                      }
                      return subField
                    }),
                  }
                : {}),
            }
          }
          return field
        })
      },
      hooks: {
        afterChange: [revalidateRedirects],
      },
    },
  }),
  nestedDocsPlugin({
    collections: ['categories'],
    generateURL: (docs) => docs.reduce((url, doc) => `${url}/${doc.slug}`, ''),
  }),
  seoPlugin({
    generateTitle,
    generateURL,
  }),
  formBuilderPlugin({
    fields: {
      payment: false,
      checkbox: {
        labels: {
          singular: {
            en: 'Checkbox',
            zh: '复选框',
          },
          plural: {
            en: 'Checkboxes',
            zh: '复选框',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
          {
            name: 'defaultValue',
            type: 'checkbox',
            label: {
              en: 'Default Value',
              zh: '默认值',
            },
          },
        ],
      },
      country: {
        labels: {
          singular: {
            en: 'Country',
            zh: '国家',
          },
          plural: {
            en: 'Countries',
            zh: '国家',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
        ],
      },
      email: {
        labels: {
          singular: {
            en: 'Email',
            zh: '邮箱',
          },
          plural: {
            en: 'Emails',
            zh: '邮箱',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
        ],
      },
      message: {
        labels: {
          singular: {
            en: 'Message',
            zh: '消息',
          },
          plural: {
            en: 'Messages',
            zh: '消息',
          },
        },
      },
      number: {
        labels: {
          singular: {
            en: 'Number',
            zh: '数字',
          },
          plural: {
            en: 'Numbers',
            zh: '数字',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
          {
            name: 'defaultValue',
            type: 'number',
            label: {
              en: 'Default Value',
              zh: '默认值',
            },
          },
        ],
      },
      select: {
        labels: {
          singular: {
            en: 'Select',
            zh: '选择',
          },
          plural: {
            en: 'Selects',
            zh: '选择',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
        ],
      },
      state: {
        labels: {
          singular: {
            en: 'State',
            zh: '州/省',
          },
          plural: {
            en: 'States',
            zh: '州/省',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
        ],
      },
      text: {
        labels: {
          singular: {
            en: 'Text',
            zh: '文本',
          },
          plural: {
            en: 'Text Fields',
            zh: '文本',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
          {
            name: 'defaultValue',
            type: 'text',
            label: {
              en: 'Default Value',
              zh: '默认值',
            },
            localized: true,
          },
        ],
      },
      textarea: {
        labels: {
          singular: {
            en: 'Text Area',
            zh: '文本区域',
          },
          plural: {
            en: 'Text Areas',
            zh: '文本区域',
          },
        },
        fields: [
          {
            name: 'name',
            type: 'text',
            label: {
              en: 'Name',
              zh: '名称',
            },
            admin: {
              description: {
                en: 'Name (lowercase, no special characters)',
                zh: '名称（小写，无特殊字符）',
              },
            },
            required: true,
          },
          {
            name: 'label',
            type: 'text',
            label: {
              en: 'Label',
              zh: '标签',
            },
            localized: true,
          },
          {
            name: 'required',
            type: 'checkbox',
            label: {
              en: 'Required',
              zh: '必填',
            },
          },
          {
            name: 'width',
            type: 'number',
            label: {
              en: 'Field Width (percentage)',
              zh: '字段宽度（百分比）',
            },
          },
          {
            name: 'defaultValue',
            type: 'textarea',
            label: {
              en: 'Default Value',
              zh: '默认值',
            },
            localized: true,
          },
        ],
      },
    },
    formOverrides: {
      labels: {
        singular: {
          en: 'Form',
          zh: '表单',
        },
        plural: {
          en: 'Forms',
          zh: '表单',
        },
      },
      fields: ({ defaultFields }) => {
        return defaultFields.map((field: any) => {
          if ('name' in field && field.name === 'confirmationType') {
            return {
              ...field,
              label: {
                en: 'Confirmation Type',
                zh: '确认类型',
              },
              options: field.options?.map((option: any) => {
                if (option.value === 'message') {
                  return {
                    ...option,
                    label: {
                      en: 'Message',
                      zh: '消息',
                    },
                  }
                }
                if (option.value === 'redirect') {
                  return {
                    ...option,
                    label: {
                      en: 'Redirect',
                      zh: '重定向',
                    },
                  }
                }
                return option
              }),
              admin: {
                ...field.admin,
                description: {
                  en: 'Choose whether to display an on-page message or redirect to a different page after they submit the form.',
                  zh: '选择在用户提交表单后显示页面消息还是重定向到不同页面。',
                },
              },
            }
          }
          if ('name' in field && field.name === 'redirect') {
            return {
              ...field,
              label: {
                en: 'Redirect',
                zh: '重定向',
              },
              ...('fields' in field && field.fields
                ? {
                    fields: field.fields.map((subField: any) => {
                      if ('name' in subField && subField.name === 'url') {
                        return {
                          ...subField,
                          label: {
                            en: 'URL to redirect to',
                            zh: '重定向到的网址',
                          },
                        }
                      }
                      return subField
                    }),
                  }
                : {}),
            }
          }
          if ('name' in field && field.name === 'confirmationMessage') {
            return {
              ...field,
              label: {
                en: 'Confirmation Message',
                zh: '确认消息',
              },
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    FixedToolbarFeature(),
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                  ]
                },
              }),
            }
          }
          if ('name' in field && field.name === 'title') {
            return {
              ...field,
              label: {
                en: 'Title',
                zh: '标题',
              },
            }
          }
          if ('name' in field && field.name === 'fields') {
            return {
              ...field,
              label: {
                en: 'Fields',
                zh: '字段',
              },
              labels: {
                singular: {
                  en: 'Field',
                  zh: '字段',
                },
                plural: {
                  en: 'Fields',
                  zh: '字段',
                },
              },
              // Override the fields within the blocks array
              blocks: field.blocks?.map((block: any) => {
                return {
                  ...block,
                  fields: block.fields?.map((blockField: any) => {
                    if (blockField.name === 'name') {
                      return {
                        ...blockField,
                        label: {
                          en: 'Name',
                          zh: '名称',
                        },
                        admin: {
                          ...blockField.admin,
                          description: {
                            en: 'Name (lowercase, no special characters)',
                            zh: '名称（小写，无特殊字符）',
                          },
                        },
                      }
                    }
                    if (blockField.name === 'label') {
                      return {
                        ...blockField,
                        label: {
                          en: 'Label',
                          zh: '标签',
                        },
                      }
                    }
                    if (blockField.name === 'width') {
                      return {
                        ...blockField,
                        label: {
                          en: 'Field Width (percentage)',
                          zh: '字段宽度（百分比）',
                        },
                      }
                    }
                    if (blockField.name === 'required') {
                      return {
                        ...blockField,
                        label: {
                          en: 'Required',
                          zh: '必填',
                        },
                      }
                    }
                    if (blockField.name === 'defaultValue') {
                      return {
                        ...blockField,
                        label: {
                          en: 'Default Value',
                          zh: '默认值',
                        },
                      }
                    }
                    return blockField
                  }),
                }
              }),
            }
          }
          if ('name' in field && field.name === 'submitButtonLabel') {
            return {
              ...field,
              label: {
                en: 'Submit Button Label',
                zh: '提交按钮标签',
              },
            }
          }
          if ('name' in field && field.name === 'emails') {
            return {
              ...field,
              label: {
                en: 'Emails',
                zh: '邮件',
              },
              labels: {
                singular: {
                  en: 'Email',
                  zh: '邮件',
                },
                plural: {
                  en: 'Emails',
                  zh: '邮件',
                },
              },
              admin: {
                ...field.admin,
                description: {
                  en: "Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.",
                  zh: '表单提交时发送自定义邮件。使用逗号分隔列表向多个收件人发送相同邮件。要引用此表单中的值，请用双花括号包围字段名称，例如 {{firstName}}。您可以使用通配符 {{*}} 输出所有数据，使用 {{*:table}} 将其格式化为邮件中的HTML表格。',
                },
              },
              ...('fields' in field && field.fields
                ? {
                    fields: field.fields.map((emailField: any) => {
                      // Handle row fields (nested structure)
                      if (emailField.type === 'row' && emailField.fields) {
                        return {
                          ...emailField,
                          fields: emailField.fields.map((nestedField: any) => {
                            if (nestedField.name === 'emailTo') {
                              return {
                                ...nestedField,
                                label: {
                                  en: 'Email To',
                                  zh: '收件人',
                                },
                                admin: {
                                  ...nestedField.admin,
                                  placeholder: {
                                    en: '"Email Sender" <<EMAIL>>',
                                    zh: '"邮件发送者" <<EMAIL>>',
                                  },
                                },
                              }
                            }
                            if (nestedField.name === 'cc') {
                              return {
                                ...nestedField,
                                label: {
                                  en: 'CC',
                                  zh: '抄送',
                                },
                                admin: {
                                  ...nestedField.admin,
                                  placeholder: {
                                    en: '"CC Recipient" <<EMAIL>>',
                                    zh: '"抄送收件人" <<EMAIL>>',
                                  },
                                },
                              }
                            }
                            if (nestedField.name === 'bcc') {
                              return {
                                ...nestedField,
                                label: {
                                  en: 'BCC',
                                  zh: '密送',
                                },
                                admin: {
                                  ...nestedField.admin,
                                  placeholder: {
                                    en: '"BCC Recipient" <<EMAIL>>',
                                    zh: '"密送收件人" <<EMAIL>>',
                                  },
                                },
                              }
                            }
                            if (nestedField.name === 'replyTo') {
                              return {
                                ...nestedField,
                                label: {
                                  en: 'Reply To',
                                  zh: '回复到',
                                },
                                admin: {
                                  ...nestedField.admin,
                                  placeholder: {
                                    en: '"Reply To" <<EMAIL>>',
                                    zh: '"回复到" <<EMAIL>>',
                                  },
                                },
                              }
                            }
                            if (nestedField.name === 'emailFrom') {
                              return {
                                ...nestedField,
                                label: {
                                  en: 'Email From',
                                  zh: '发件人',
                                },
                                admin: {
                                  ...nestedField.admin,
                                  placeholder: {
                                    en: '"Email From" <<EMAIL>>',
                                    zh: '"邮件来源" <<EMAIL>>',
                                  },
                                },
                              }
                            }
                            return nestedField
                          }),
                        }
                      }

                      // Handle direct fields
                      if ('name' in emailField && emailField.name === 'subject') {
                        return {
                          ...emailField,
                          label: {
                            en: 'Subject',
                            zh: '主题',
                          },
                          defaultValue: '您收到了一条新消息。',
                          admin: {
                            ...emailField.admin,
                            placeholder: {
                              en: "You've received a new message.",
                              zh: '您收到了一条新消息。',
                            },
                          },
                        }
                      }
                      if ('name' in emailField && emailField.name === 'message') {
                        return {
                          ...emailField,
                          label: {
                            en: 'Message',
                            zh: '消息',
                          },
                          admin: {
                            ...emailField.admin,
                            description: {
                              en: 'Enter the message that should be sent in this email.',
                              zh: '输入此邮件中应发送的消息。',
                            },
                            placeholder: {
                              en: 'Enter your email message here...',
                              zh: '在此输入您的邮件消息...',
                            },
                          },
                        }
                      }
                      return emailField
                    }),
                  }
                : {}),
            }
          }
          return field
        })
      },
    },
    formSubmissionOverrides: {
      labels: {
        singular: {
          en: 'Form Submission',
          zh: '表单提交',
        },
        plural: {
          en: 'Form Submissions',
          zh: '表单提交',
        },
      },
    },
  }),
  searchPlugin({
    collections: ['posts'],
    beforeSync: beforeSyncWithSearch,
    searchOverrides: {
      labels: {
        singular: {
          en: 'Search Result',
          zh: '搜索结果',
        },
        plural: {
          en: 'Search Results',
          zh: '搜索结果',
        },
      },
      fields: ({ defaultFields }) => {
        return [...defaultFields, ...searchFields]
      },
    },
  }),
  payloadCloudPlugin(),
]
